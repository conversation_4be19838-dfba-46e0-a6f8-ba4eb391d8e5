2025-08-14 12:49:38,785 INFO ipython optimized_css = """/* ===================== GLOBAL PRINT STYLES ===================== */
.print-format .letter-head {
    display: none;
    }
    
    /* Reset default spacing and alignment */
    .print-format {
        padding: 15px !important;
            font-family: 'Calibri', Arial, sans-serif;
                font-size: 11px;
                    line-height: 1.3;
                        color: #000;
                            max-width: 100%;
                            }
                            
                            .print-format td, .print-format th {
                                vertical-align: top !important;
                                    padding: 3px 5px !important;
                                    }
                                    
                                    .print-format th {
                                        color: black !important;
                                            font-weight: bold;
                                                border-bottom-width: 1px !important;
                                                }
                                                
                                                .print-format p {
                                                    margin: 0px 0px 4px !important;
                                                    }
                                                    
                                                    /* Remove default ERPNext spacing */
                                                    .frappe-control, .form-section {
                                                        margin-bottom: 0 !important;
                                                        }
                                                        
                                                        /* ===================== RESPONSIVE LAYOUT ===================== */
                                                        html, body {
                                                            height: auto !important;
                                                                overflow: visible !important;
                                                                }
                                                                
                                                                .print-format {
                                                                    display: block;
                                                                        height: auto;
                                                                            overflow: visible;
                                                                            }
                                                                            
                                                                            /* ===================== PAGE BREAK HANDLING ===================== */
                                                                            @media print {
                                                                                @page {
                                                                                        margin: 8mm 6mm;
                                                                                                size: A4;
                                                                                                    }
                                                                                                        
                                                                                                            .print-format {
                                                                                                                    padding: 6px !important;
                                                                                                                            font-size: 10px;
                                                                                                                                }
                                                                                                                                    
                                                                                                                                        /* Prevent page breaks inside important sections */
                                                                                                                                            .header-section, .customer-details, .supplier-details, .order-details {
                                                                                                                                                    page-break-inside: avoid;
                                                                                                                                                        }
                                                                                                                                                            
                                                                                                                                                                /* Allow page breaks between item rows if needed */
                                                                                                                                                                    .items-table tbody tr {
                                                                                                                                                                            page-break-inside: avoid;
                                                                                                                                                                                    page-break-after: auto;
                                                                                                                                                                                        }
                                                                                                                                                                                            
                                                                                                                                                                                                /* Keep footer sections together */
                                                                                                                                                                                                    .footer-section {
                                                                                                                                                                                                            page-break-inside: avoid;
                                                                                                                                                                                                                    margin-top: 10px;
                                                                                                                                                                                                                        }
                                                                                                                                                                                                                            
                                                                                                                                                                                                                                /* Optimize spacing for print */
                                                                                                                                                                                                                                    .responsive-margin {
                                                                                                                                                                                                                                            margin-top: 8px !important;
                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                
                                                                                                                                                                                                                                                /* ===================== DYNAMIC CONTENT HANDLING ===================== */
                                                                                                                                                                                                                                                .content-wrapper {
                                                                                                                                                                                                                                                    display: flex;
                                                                                                                                                                                                                                                        flex-direction: column;
                                                                                                                                                                                                                                                            min-height: auto;
                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                            .items-section {
                                                                                                                                                                                                                                                                flex: 1;
                                                                                                                                                                                                                                                                    margin: 12px 0;
                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                    .footer-section {
                                                                                                                                                                                                                                                                        margin-top: auto;
                                                                                                                                                                                                                                                                            padding-top: 12px;
                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                            /* ===================== ALIGNMENT FIXES ===================== */
                                                                                                                                                                                                                                                                            .text-left { text-align: left !important; }
                                                                                                                                                                                                                                                                            .text-center { text-align: center !important; }
                                                                                                                                                                                                                                                                            .text-right { text-align: right !important; }
                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                            /* ===================== TABLE IMPROVEMENTS ===================== */
                                                                                                                                                                                                                                                                            .items-table {
                                                                                                                                                                                                                                                                                width: 100%;
                                                                                                                                                                                                                                                                                    border-collapse: collapse;
                                                                                                                                                                                                                                                                                        margin: 12px 0;
                                                                                                                                                                                                                                                                                            font-size: 11px;
                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                            .items-table th {
                                                                                                                                                                                                                                                                                                border-top: 1px solid #000;
                                                                                                                                                                                                                                                                                                    border-bottom: 1px solid #000;
                                                                                                                                                                                                                                                                                                        padding: 4px 6px;
                                                                                                                                                                                                                                                                                                            font-weight: bold;
                                                                                                                                                                                                                                                                                                                background-color: #f8f9fa;
                                                                                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                                                                                
                                                                                                                                                                                                                                                                                                                .items-table td {
                                                                                                                                                                                                                                                                                                                    border-bottom: 1px solid #ddd;
                                                                                                                                                                                                                                                                                                                        padding: 3px 6px;
                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                        
                                                                                                                                                                                                                                                                                                                        /* Handle many items by reducing row height */
                                                                                                                                                                                                                                                                                                                        .items-table.many-items {
                                                                                                                                                                                                                                                                                                                            font-size: 10px;
                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                                                            .items-table.many-items td,
                                                                                                                                                                                                                                                                                                                            .items-table.many-items th {
                                                                                                                                                                                                                                                                                                                                padding: 2px 4px;
                                                                                                                                                                                                                                                                                                                                    line-height: 1.1;
                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                                                                                    /* ===================== RESPONSIVE SPACING ===================== */
                                                                                                                                                                                                                                                                                                                                    .responsive-margin {
                                                                                                                                                                                                                                                                                                                                        margin-top: 12px;
                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                        
                                                                                                                                                                                                                                                                                                                                        @media print {
                                                                                                                                                                                                                                                                                                                                            .responsive-margin {
                                                                                                                                                                                                                                                                                                                                                    margin-top: 6px;
                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                        
                                                                                                                                                                                                                                                                                                                                                        /* ===================== FOOTER LAYOUT ===================== */
                                                                                                                                                                                                                                                                                                                                                        .footer-layout {
                                                                                                                                                                                                                                                                                                                                                            display: flex;
                                                                                                                                                                                                                                                                                                                                                                justify-content: space-between;
                                                                                                                                                                                                                                                                                                                                                                    align-items: flex-start;
                                                                                                                                                                                                                                                                                                                                                                        margin-top: 15px;
                                                                                                                                                                                                                                                                                                                                                                            gap: 20px;
                                                                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                                                                                                            .footer-left {
                                                                                                                                                                                                                                                                                                                                                                                flex: 1;
                                                                                                                                                                                                                                                                                                                                                                                    max-width: 45%;
                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                                                                                                                                    .footer-right {
                                                                                                                                                                                                                                                                                                                                                                                        flex: 1;
                                                                                                                                                                                                                                                                                                                                                                                            max-width: 45%;
                                                                                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                                                                                                                            @media print {
                                                                                                                                                                                                                                                                                                                                                                                                .footer-layout {
                                                                                                                                                                                                                                                                                                                                                                                                        margin-top: 8px;
                                                                                                                                                                                                                                                                                                                                                                                                                gap: 10px;
                                                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                                                                                                                                                                    """
2025-08-14 12:49:38,791 INFO ipython print("CSS defined successfully!")
2025-08-14 12:49:38,791 INFO ipython # Update Rubis Invoice
2025-08-14 12:49:38,791 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Invoice")
2025-08-14 12:49:38,792 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,792 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,792 INFO ipython doc.save()
2025-08-14 12:49:38,792 INFO ipython print("✅ Rubis Invoice updated!")
2025-08-14 12:49:38,792 INFO ipython # Update Rubis Sales Order
2025-08-14 12:49:38,793 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Sales Order")
2025-08-14 12:49:38,793 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,793 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,793 INFO ipython doc.save()
2025-08-14 12:49:38,793 INFO ipython print("✅ Rubis Sales Order updated!")
2025-08-14 12:49:38,794 INFO ipython # Update Rubis Purchase Order
2025-08-14 12:49:38,794 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Purchase Order")
2025-08-14 12:49:38,794 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,794 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,794 INFO ipython doc.save()
2025-08-14 12:49:38,794 INFO ipython print("✅ Rubis Purchase Order updated!")
2025-08-14 12:49:38,795 INFO ipython # Update Rubis Purchase Receipt
2025-08-14 12:49:38,795 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Purchase Receipt")
2025-08-14 12:49:38,795 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,795 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,795 INFO ipython doc.save()
2025-08-14 12:49:38,796 INFO ipython print("✅ Rubis Purchase Receipt updated!")
2025-08-14 12:49:38,796 INFO ipython # Commit all changes
2025-08-14 12:49:38,796 INFO ipython frappe.db.commit()
2025-08-14 12:49:38,796 INFO ipython print("🎉 All print formats updated successfully in your rubis site!")
2025-08-14 12:49:38,796 INFO ipython === session end ===
2025-08-18 09:08:00,866 INFO ipython === bench console session ===
2025-08-18 09:08:00,866 INFO ipython from csf_tz.patches.setup_biometrics_system import validate_biometrics_setup
2025-08-18 09:08:00,867 INFO ipython issues = validate_biometrics_setup()
2025-08-18 09:08:00,867 INFO ipython print("Validation Issues:", issues)
2025-08-18 09:08:00,867 INFO ipython from frappe.utils import getdate
2025-08-18 09:08:00,867 INFO ipython print("Available utils:", dir(frappe.utils)[:10])
2025-08-18 09:08:00,867 INFO ipython [x for x in dir(frappe.utils) if 'year' in x.lower()]
2025-08-18 09:08:00,867 INFO ipython === session end ===
2025-08-18 09:14:01,656 INFO ipython === bench console session ===
2025-08-18 09:14:01,656 INFO ipython from csf_tz.utils.tanzania_holidays import create_tanzania_holiday_list
2025-08-18 09:14:01,656 INFO ipython holiday_list = create_tanzania_holiday_list(2024)
2025-08-18 09:14:01,656 INFO ipython print("Created holiday list:", holiday_list)
2025-08-18 09:14:01,656 INFO ipython from csf_tz.utils.tanzania_holidays import create_tanzania_holiday_list
2025-08-18 09:14:01,656 INFO ipython holiday_list = create_tanzania_holiday_list(2024)
2025-08-18 09:14:01,657 INFO ipython print("Created holiday list:", holiday_list)
2025-08-18 09:14:01,657 INFO ipython from datetime import date
2025-08-18 09:14:01,658 INFO ipython import calendar
2025-08-18 09:14:01,658 INFO ipython april_7_2024 = date(2024, 4, 7)
2025-08-18 09:14:01,658 INFO ipython print("April 7, 2024 is a:", calendar.day_name[april_7_2024.weekday()])
2025-08-18 09:14:01,658 INFO ipython === session end ===
2025-08-18 09:24:47,729 INFO ipython === bench console session ===
2025-08-18 09:24:47,730 INFO ipython from csf_tz.utils.tanzania_holidays import create_tanzania_holiday_list
2025-08-18 09:24:47,730 INFO ipython holiday_list = create_tanzania_holiday_list(2024)
2025-08-18 09:24:47,730 INFO ipython print("Created holiday list:", holiday_list)
2025-08-18 09:24:47,730 INFO ipython from csf_tz.utils.tanzania_holidays import get_saturday_overtime_hours
2025-08-18 09:24:47,730 INFO ipython from datetime import date
2025-08-18 09:24:47,730 INFO ipython # Test Saturday overtime calculation
2025-08-18 09:24:47,730 INFO ipython saturday_date = date(2024, 4, 6)  # A Saturday
2025-08-18 09:24:47,730 INFO ipython saturday_ot = get_saturday_overtime_hours("12:00:00", "17:00:00", saturday_date)
2025-08-18 09:24:47,730 INFO ipython print(f"Saturday overtime for 12:00-17:00: {saturday_ot}")
2025-08-18 09:24:47,731 INFO ipython # Test non-Saturday
2025-08-18 09:24:47,731 INFO ipython monday_date = date(2024, 4, 8)  # A Monday
2025-08-18 09:24:47,731 INFO ipython monday_ot = get_saturday_overtime_hours("12:00:00", "17:00:00", monday_date)
2025-08-18 09:24:47,731 INFO ipython print(f"Monday overtime (should be 0): {monday_ot}")
2025-08-18 09:24:47,731 INFO ipython from csf_tz.patches.setup_biometrics_system import validate_biometrics_setup
2025-08-18 09:24:47,731 INFO ipython issues = validate_biometrics_setup()
2025-08-18 09:24:47,731 INFO ipython print("Validation Issues:", issues)
2025-08-18 09:24:47,731 INFO ipython from csf_tz.patches.setup_biometrics_system import apply_custom_fields
2025-08-18 09:24:47,731 INFO ipython apply_custom_fields()
2025-08-18 09:24:47,732 INFO ipython print("Custom fields applied")
2025-08-18 09:24:47,732 INFO ipython from csf_tz.patches.setup_biometrics_system import execute
2025-08-18 09:24:47,732 INFO ipython execute()
2025-08-18 09:24:47,732 INFO ipython print("Full setup completed")
2025-08-18 09:24:47,732 INFO ipython === session end ===
2025-08-28 10:59:42,766 INFO ipython === bench console session ===
2025-08-28 10:59:42,767 INFO ipython from csf_tz.csf_tz.report.sales_cycle_report.sales_cycle_report import execute
2025-08-28 10:59:42,767 INFO ipython filters = {"from_date": "2025-08-01", "to_date": "2025-08-28"}
2025-08-28 10:59:42,767 INFO ipython columns, data = execute(filters)
2025-08-28 10:59:42,768 INFO ipython print("Report executed successfully!")
2025-08-28 10:59:42,768 INFO ipython print(f"Number of columns: {len(columns)}")
2025-08-28 10:59:42,768 INFO ipython print(f"Number of data rows: {len(data)}")
2025-08-28 10:59:42,768 INFO ipython frappe.db.describe("Sales Order Item")
2025-08-28 10:59:42,768 INFO ipython === session end ===
2025-08-28 11:03:59,882 INFO ipython === bench console session ===
2025-08-28 11:03:59,882 INFO ipython from csf_tz.csf_tz.report.sales_cycle_report.sales_cycle_report import execute
2025-08-28 11:03:59,883 INFO ipython filters = {"from_date": "2025-08-01", "to_date": "2025-08-28"}
2025-08-28 11:03:59,883 INFO ipython columns, data = execute(filters)
2025-08-28 11:03:59,883 INFO ipython print("Report executed successfully!")
2025-08-28 11:03:59,883 INFO ipython print(f"Number of columns: {len(columns)}")
2025-08-28 11:03:59,884 INFO ipython print(f"Number of data rows: {len(data)}")
2025-08-28 11:03:59,884 INFO ipython bench --site rubis console
2025-08-28 11:03:59,884 INFO ipython bench --site rubis console
2025-08-28 11:03:59,884 INFO ipython bench --site rubis console
2025-08-28 11:03:59,884 INFO ipython === session end ===
2025-08-28 11:06:58,629 INFO ipython === bench console session ===
2025-08-28 11:06:58,629 INFO ipython from csf_tz.csf_tz.report.sales_cycle_report.sales_cycle_report import execute
2025-08-28 11:06:58,629 INFO ipython filters = {"from_date": "2025-08-01", "to_date": "2025-08-28"}
2025-08-28 11:06:58,630 INFO ipython columns, data = execute(filters)
2025-08-28 11:06:58,630 INFO ipython print("Report executed successfully!")
2025-08-28 11:06:58,630 INFO ipython print(f"Number of columns: {len(columns)}")
2025-08-28 11:06:58,630 INFO ipython print(f"Number of data rows: {len(data)}")
2025-08-28 11:06:58,630 INFO ipython bench --site rubis console
2025-08-28 11:06:58,630 INFO ipython === session end ===
2025-09-04 17:29:08,223 INFO ipython === bench console session ===
2025-09-04 17:29:08,235 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site explore console
2025-09-04 17:29:08,235 INFO ipython frappe.get_meta("Gate Pass").get_field("workflow_state")
2025-09-04 17:29:08,236 INFO ipython frappe.db.get_list("DocType", filters={"name": ["like", "%Gate%"]}, fields=["name"])
2025-09-04 17:29:08,236 INFO ipython === session end ===
2025-09-17 15:22:02,217 INFO ipython === bench console session ===
2025-09-17 15:22:02,219 INFO ipython frappe.db.get_list("Dashboard", fields=["name", "dashboard_name"])
2025-09-17 15:22:02,219 INFO ipython frappe.get_installed_apps()
2025-09-17 15:22:02,219 INFO ipython === session end ===
2025-09-17 15:32:08,140 INFO ipython === bench console session ===
2025-09-17 15:32:08,141 INFO ipython exec(open('apps/sdg_reporting/sdg_reporting/sdg_reporting/create_simple_dashboards.py').read())
2025-09-17 15:32:08,142 INFO ipython import os; os.getcwd()
2025-09-17 15:32:08,142 INFO ipython exec(open('../apps/sdg_reporting/sdg_reporting/sdg_reporting/create_simple_dashboards.py').read())
2025-09-17 15:32:08,142 INFO ipython create_simple_dashboards()
2025-09-17 15:32:08,142 INFO ipython import frappe
2025-09-17 15:32:08,142 INFO ipython import json
2025-09-17 15:32:08,142 INFO ipython # Create Number Cards
2025-09-17 15:32:08,142 INFO ipython cards = [
    {
            "name": "Total SDG Goals",
                    "label": "Total SDG Goals",
                            "type": "Document Type",
                                    "document_type": "SDG Goal",
                                            "function": "Count",
                                                    "color": "#2E86AB",
                                                            "is_public": 1,
                                                                    "module": "Sdg Reporting",
                                                                        },
                                                                            {
                                                                                    "name": "Active Sustainability Metrics",
                                                                                            "label": "Active Sustainability Metrics",
                                                                                                    "type": "Document Type",
                                                                                                            "document_type": "Sustainability Metric",
                                                                                                                    "function": "Count",
                                                                                                                            "color": "#A23B72",
                                                                                                                                    "is_public": 1,
                                                                                                                                            "module": "Sdg Reporting",
                                                                                                                                                },
                                                                                                                                                ]
2025-09-17 15:32:08,143 INFO ipython for card_data in cards:
        if not frappe.db.exists("Number Card", card_data["name"]):
                    try:
                                    doc = frappe.new_doc("Number Card")
                                                doc.update(card_data)
2025-09-17 15:32:08,143 INFO ipython             doc.insert(ignore_permissions=True)
2025-09-17 15:32:08,143 INFO ipython             print(f"Created Number Card: {card_data['name']}")
2025-09-17 15:32:08,143 INFO ipython         except Exception as e:
                print(f"Error creating Number Card {card_data['name']}: {str(e)}")
2025-09-17 15:32:08,143 INFO ipython # Create a simple number card
2025-09-17 15:32:08,143 INFO ipython doc = frappe.new_doc("Number Card")
2025-09-17 15:32:08,143 INFO ipython doc.name = "Total SDG Goals"
2025-09-17 15:32:08,143 INFO ipython doc.label = "Total SDG Goals"
2025-09-17 15:32:08,143 INFO ipython doc.type = "Document Type"
2025-09-17 15:32:08,144 INFO ipython doc.document_type = "SDG Goal"
2025-09-17 15:32:08,144 INFO ipython doc.function = "Count"
2025-09-17 15:32:08,144 INFO ipython doc.color = "#2E86AB"
2025-09-17 15:32:08,144 INFO ipython doc.is_public = 1
2025-09-17 15:32:08,144 INFO ipython doc.module = "Sdg Reporting"
2025-09-17 15:32:08,144 INFO ipython doc.insert(ignore_permissions=True)
2025-09-17 15:32:08,144 INFO ipython print("Created Number Card: Total SDG Goals")
2025-09-17 15:32:08,144 INFO ipython # Create a simple dashboard chart
2025-09-17 15:32:08,144 INFO ipython chart = frappe.new_doc("Dashboard Chart")
2025-09-17 15:32:08,144 INFO ipython chart.name = "SDG Metrics Count"
2025-09-17 15:32:08,144 INFO ipython chart.chart_name = "SDG Metrics Count"
2025-09-17 15:32:08,145 INFO ipython chart.chart_type = "Count"
2025-09-17 15:32:08,145 INFO ipython chart.document_type = "Sustainability Metric"
2025-09-17 15:32:08,145 INFO ipython chart.based_on = "creation"
2025-09-17 15:32:08,145 INFO ipython chart.timeseries = 1
2025-09-17 15:32:08,145 INFO ipython chart.time_interval = "Monthly"
2025-09-17 15:32:08,145 INFO ipython chart.timespan = "Last Year"
2025-09-17 15:32:08,145 INFO ipython chart.type = "Line"
2025-09-17 15:32:08,145 INFO ipython chart.color = "#2E86AB"
2025-09-17 15:32:08,146 INFO ipython chart.is_public = 1
2025-09-17 15:32:08,146 INFO ipython chart.module = "Sdg Reporting"
2025-09-17 15:32:08,146 INFO ipython chart.insert(ignore_permissions=True)
2025-09-17 15:32:08,146 INFO ipython print("Created Dashboard Chart: SDG Metrics Count")
2025-09-17 15:32:08,146 INFO ipython # Create a simple dashboard chart with filters_json
2025-09-17 15:32:08,146 INFO ipython chart = frappe.new_doc("Dashboard Chart")
2025-09-17 15:32:08,146 INFO ipython chart.name = "SDG Metrics Count"
2025-09-17 15:32:08,146 INFO ipython chart.chart_name = "SDG Metrics Count"
2025-09-17 15:32:08,147 INFO ipython chart.chart_type = "Count"
2025-09-17 15:32:08,147 INFO ipython chart.document_type = "Sustainability Metric"
2025-09-17 15:32:08,147 INFO ipython chart.based_on = "creation"
2025-09-17 15:32:08,147 INFO ipython chart.timeseries = 1
2025-09-17 15:32:08,147 INFO ipython chart.time_interval = "Monthly"
2025-09-17 15:32:08,147 INFO ipython chart.timespan = "Last Year"
2025-09-17 15:32:08,147 INFO ipython chart.type = "Line"
2025-09-17 15:32:08,147 INFO ipython chart.color = "#2E86AB"
2025-09-17 15:32:08,147 INFO ipython chart.is_public = 1
2025-09-17 15:32:08,148 INFO ipython chart.module = "Sdg Reporting"
2025-09-17 15:32:08,148 INFO ipython chart.filters_json = "[]"
2025-09-17 15:32:08,148 INFO ipython chart.insert(ignore_permissions=True)
2025-09-17 15:32:08,148 INFO ipython print("Created Dashboard Chart: SDG Metrics Count")
2025-09-17 15:32:08,148 INFO ipython # Create the dashboard
2025-09-17 15:32:08,148 INFO ipython dashboard = frappe.new_doc("Dashboard")
2025-09-17 15:32:08,148 INFO ipython dashboard.name = "SDG Overview"
2025-09-17 15:32:08,149 INFO ipython dashboard.dashboard_name = "SDG Overview"
2025-09-17 15:32:08,149 INFO ipython dashboard.module = "Sdg Reporting"
2025-09-17 15:32:08,149 INFO ipython dashboard.is_standard = 1
2025-09-17 15:32:08,149 INFO ipython # Add the chart
2025-09-17 15:32:08,149 INFO ipython dashboard.append("charts", {
    "chart": "SDG Metrics Count",
        "width": "Full"
        })
2025-09-17 15:32:08,149 INFO ipython # Add the card
2025-09-17 15:32:08,149 INFO ipython dashboard.append("cards", {
    "card": "Total SDG Goals"
    })
2025-09-17 15:32:08,149 INFO ipython dashboard.insert(ignore_permissions=True)
2025-09-17 15:32:08,149 INFO ipython print("Created Dashboard: SDG Overview")
2025-09-17 15:32:08,150 INFO ipython # Set chart as standard
2025-09-17 15:32:08,150 INFO ipython chart_doc = frappe.get_doc("Dashboard Chart", "SDG Metrics Count")
2025-09-17 15:32:08,150 INFO ipython chart_doc.is_standard = 1
2025-09-17 15:32:08,150 INFO ipython chart_doc.save(ignore_permissions=True)
2025-09-17 15:32:08,150 INFO ipython # Set card as standard
2025-09-17 15:32:08,150 INFO ipython card_doc = frappe.get_doc("Number Card", "Total SDG Goals")
2025-09-17 15:32:08,150 INFO ipython card_doc.is_standard = 1
2025-09-17 15:32:08,150 INFO ipython card_doc.save(ignore_permissions=True)
2025-09-17 15:32:08,151 INFO ipython print("Set chart and card as standard")
2025-09-17 15:32:08,151 INFO ipython # Create the dashboard again
2025-09-17 15:32:08,151 INFO ipython dashboard = frappe.new_doc("Dashboard")
2025-09-17 15:32:08,151 INFO ipython dashboard.name = "SDG Overview"
2025-09-17 15:32:08,151 INFO ipython dashboard.dashboard_name = "SDG Overview"
2025-09-17 15:32:08,151 INFO ipython dashboard.module = "Sdg Reporting"
2025-09-17 15:32:08,151 INFO ipython dashboard.is_standard = 1
2025-09-17 15:32:08,151 INFO ipython # Add the chart
2025-09-17 15:32:08,151 INFO ipython dashboard.append("charts", {
    "chart": "SDG Metrics Count",
        "width": "Full"
        })
2025-09-17 15:32:08,152 INFO ipython # Add the card
2025-09-17 15:32:08,152 INFO ipython dashboard.append("cards", {
    "card": "Total SDG Goals"
    })
2025-09-17 15:32:08,152 INFO ipython dashboard.insert(ignore_permissions=True)
2025-09-17 15:32:08,152 INFO ipython print("Created Dashboard: SDG Overview")
2025-09-17 15:32:08,152 INFO ipython # Create sample SDG Goals
2025-09-17 15:32:08,152 INFO ipython sample_goals = [
    {"goal_no": "1", "goal_name": "No Poverty", "description": "End poverty in all its forms everywhere"},
        {"goal_no": "2", "goal_name": "Zero Hunger", "description": "End hunger, achieve food security and improved nutrition"},
            {"goal_no": "3", "goal_name": "Good Health and Well-being", "description": "Ensure healthy lives and promote well-being for all"},
            ]
2025-09-17 15:32:08,152 INFO ipython for goal_data in sample_goals:
        if not frappe.db.exists("SDG Goal", goal_data["goal_no"]):
                    doc = frappe.new_doc("SDG Goal")
                            doc.update(goal_data)
2025-09-17 15:32:08,152 INFO ipython         doc.insert(ignore_permissions=True)
2025-09-17 15:32:08,153 INFO ipython         print(f"Created SDG Goal: {goal_data['goal_no']}")
2025-09-17 15:32:08,153 INFO ipython # Create sample Sustainability Metrics
2025-09-17 15:32:08,153 INFO ipython sample_metrics = [
    {
            "metric_name": "Maternal Mortality Rate",
                    "esg_pillar": "Social",
                            "sdg_link": "3",
                                    "unit": "per 100,000 live births",
                                            "measures": "Health outcomes for mothers"
                                                },
                                                    {
                                                            "metric_name": "Healthcare Coverage",
                                                                    "esg_pillar": "Social", 
                                                                            "sdg_link": "3",
                                                                                    "unit": "percentage",
                                                                                            "measures": "Population with health insurance"
                                                                                                },
                                                                                                ]
2025-09-17 15:32:08,153 INFO ipython for metric_data in sample_metrics:
        if not frappe.db.exists("Sustainability Metric", metric_data["metric_name"]):
                    doc = frappe.new_doc("Sustainability Metric")
                            doc.update(metric_data)
2025-09-17 15:32:08,154 INFO ipython         doc.insert(ignore_permissions=True)
2025-09-17 15:32:08,154 INFO ipython         print(f"Created Sustainability Metric: {metric_data['metric_name']}")
2025-09-17 15:32:08,154 INFO ipython frappe.db.commit()
2025-09-17 15:32:08,154 INFO ipython print("Sample data created and committed!")
2025-09-17 15:32:08,154 INFO ipython frappe.db.get_list("Dashboard", fields=["name", "dashboard_name"], filters={"module": "Sdg Reporting"})
2025-09-17 15:32:08,154 INFO ipython === session end ===
