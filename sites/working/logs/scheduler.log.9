2025-09-11 12:04:39,770 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 12:04:39,781 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 12:16:45,815 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-11 12:16:45,820 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-11 12:16:45,826 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-11 12:16:45,854 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-11 12:16:45,876 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-11 12:16:45,895 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-11 12:16:45,898 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-11 12:16:45,913 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-11 12:16:45,983 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-11 12:16:45,994 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-11 13:01:05,308 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 13:01:05,312 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 13:01:05,325 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 13:01:05,335 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 13:01:05,344 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 13:01:05,355 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 13:01:05,377 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 13:01:05,382 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 13:01:05,386 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 13:01:05,387 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 13:01:05,391 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 13:01:05,398 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 13:01:05,404 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 13:01:05,406 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 14:01:28,900 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 14:01:28,902 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 14:01:28,920 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 14:01:28,921 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 14:01:28,929 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 14:01:28,934 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 14:01:28,938 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 14:01:28,942 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 14:01:28,956 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 14:01:28,964 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 14:01:28,968 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 14:01:28,974 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 14:01:28,975 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 14:01:28,985 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 14:02:29,471 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 14:02:29,477 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 14:02:29,483 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 14:02:29,490 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 14:02:29,497 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 14:02:29,500 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 14:02:29,501 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 14:02:29,509 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 14:02:29,515 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 14:02:29,533 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 14:02:29,538 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 14:02:29,543 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 14:02:29,549 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 14:02:29,556 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 14:03:29,726 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 14:03:29,732 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 14:03:29,734 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 14:03:29,753 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 14:03:29,756 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 14:03:29,758 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 14:03:29,772 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 14:03:29,780 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 14:03:29,785 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 14:03:29,805 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 14:03:29,811 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 14:03:29,813 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 14:03:29,817 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 14:03:29,829 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 14:05:30,689 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-11 14:05:30,690 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-11 14:05:30,694 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-11 14:05:30,702 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-11 14:05:30,732 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-11 14:05:30,735 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-11 15:01:53,513 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 15:01:53,518 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-11 15:01:53,520 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for working
2025-09-11 15:01:53,524 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 15:01:53,526 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for working
2025-09-11 15:01:53,536 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-11 15:01:53,538 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-11 15:01:53,539 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 15:01:53,547 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-11 15:01:53,551 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 15:01:53,552 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-11 15:01:53,554 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for working
2025-09-11 15:01:53,557 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-11 15:01:53,560 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for working
2025-09-11 15:01:53,564 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for working
2025-09-11 15:01:53,566 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-11 15:01:53,568 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-11 15:01:53,571 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-11 15:01:53,574 ERROR scheduler Skipped queueing drive.api.permissions.auto_delete_expired_perms because it was found in queue for working
2025-09-11 15:01:53,576 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 15:01:53,578 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 15:01:53,582 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 15:01:53,584 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 15:01:53,586 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 15:01:53,591 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for working
2025-09-11 15:01:53,593 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-11 15:01:53,595 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for working
2025-09-11 15:01:53,601 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-11 15:01:53,607 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-11 15:01:53,608 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 15:01:53,609 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 15:01:53,611 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for working
2025-09-11 15:01:53,612 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 15:01:53,616 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-11 15:01:53,620 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for working
2025-09-11 15:01:53,624 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-11 15:01:53,630 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-11 15:01:53,632 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-11 15:01:53,634 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 15:01:53,635 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 15:01:53,639 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-11 15:02:53,936 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 15:02:53,938 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 15:02:53,942 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 15:02:53,948 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 15:02:53,961 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 15:02:53,965 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 15:02:53,967 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 15:02:53,973 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 15:02:53,986 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 15:02:54,008 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 15:02:54,013 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 15:02:54,014 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 15:02:54,028 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 15:02:54,036 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 15:03:54,456 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 15:03:54,461 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 15:03:54,466 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 15:03:54,478 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 15:03:54,480 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 15:03:54,485 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 15:03:54,489 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 15:03:54,495 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 15:03:54,496 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 15:03:54,519 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 15:03:54,539 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 15:03:54,545 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 15:03:54,559 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 15:03:54,561 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 16:01:18,268 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 16:01:18,274 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 16:01:18,285 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 16:01:18,287 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 16:01:18,299 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 16:01:18,300 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 16:01:18,303 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 16:01:18,315 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 16:01:18,318 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 16:01:18,326 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 16:01:18,336 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 16:01:18,343 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 16:01:18,346 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 16:01:18,351 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 16:02:18,586 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 16:02:18,589 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 16:02:18,590 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 16:02:18,609 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 16:02:18,611 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 16:02:18,615 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 16:02:18,620 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 16:02:18,623 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 16:02:18,634 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 16:02:18,649 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 16:02:18,664 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 16:02:18,673 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-11 16:02:18,676 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 16:02:18,678 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 16:05:19,853 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-11 16:05:19,903 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-11 16:05:19,928 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-11 16:05:19,945 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-11 16:05:19,954 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-11 16:05:19,978 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-11 17:01:43,335 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-11 17:01:43,345 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-11 17:01:43,347 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-11 17:01:43,356 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-11 17:01:43,367 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-11 17:01:43,369 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-11 17:01:43,376 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-11 17:01:43,378 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-11 17:01:43,380 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-11 17:01:43,394 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-11 17:01:43,406 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-11 17:01:43,408 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-11 17:01:43,418 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-11 17:01:43,432 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 08:23:59,159 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 08:23:59,163 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:23:59,167 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 08:23:59,170 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-09-12 08:23:59,174 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-09-12 08:23:59,178 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-09-12 08:23:59,181 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-09-12 08:23:59,213 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 08:23:59,228 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 08:23:59,234 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-09-12 08:23:59,236 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 08:23:59,239 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-09-12 08:23:59,244 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 08:23:59,255 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-09-12 08:23:59,263 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 08:23:59,280 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 08:23:59,289 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 08:23:59,299 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 08:23:59,303 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 08:23:59,306 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-09-12 08:23:59,308 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-09-12 08:23:59,313 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-09-12 08:23:59,317 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 08:23:59,322 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-09-12 08:23:59,335 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-09-12 08:23:59,337 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 08:23:59,344 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-09-12 08:23:59,348 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-09-12 08:23:59,367 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-09-12 08:23:59,369 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-09-12 08:23:59,377 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:23:59,379 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-09-12 08:23:59,381 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 08:24:59,707 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-09-12 08:24:59,711 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-09-12 08:24:59,714 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 08:24:59,716 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 08:24:59,719 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-09-12 08:24:59,732 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 08:24:59,734 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-09-12 08:24:59,737 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:24:59,743 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 08:24:59,744 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-09-12 08:24:59,747 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 08:24:59,750 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 08:24:59,752 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 08:24:59,754 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 08:24:59,762 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-09-12 08:24:59,764 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:24:59,767 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-09-12 08:24:59,771 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-09-12 08:24:59,777 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 08:24:59,781 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-09-12 08:24:59,790 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 08:24:59,793 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-09-12 08:24:59,796 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-09-12 08:24:59,798 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-09-12 08:24:59,800 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-09-12 08:24:59,803 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-09-12 08:24:59,807 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-09-12 08:24:59,813 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-09-12 08:24:59,815 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 08:24:59,816 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-09-12 08:24:59,824 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 08:24:59,827 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 08:24:59,828 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 08:26:00,591 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-09-12 08:26:00,599 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 08:26:00,607 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 08:26:00,612 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-09-12 08:26:00,622 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-09-12 08:26:00,626 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-09-12 08:26:00,633 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 08:26:00,640 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-09-12 08:26:00,645 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-09-12 08:26:00,651 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-09-12 08:26:00,667 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 08:26:00,669 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-09-12 08:26:00,673 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-09-12 08:26:00,676 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 08:26:00,690 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 08:26:00,692 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-09-12 08:26:00,703 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-09-12 08:26:00,709 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 08:26:00,717 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-09-12 08:26:00,732 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 08:26:00,738 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-09-12 08:26:00,746 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:26:00,752 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 08:26:00,756 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:26:00,759 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 08:26:00,761 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 08:26:00,765 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 08:26:00,767 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-09-12 08:26:00,783 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 08:26:00,786 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 08:26:00,792 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 08:26:00,798 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 08:26:00,802 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 08:26:00,804 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 08:26:00,816 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-09-12 08:26:00,818 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-09-12 08:26:00,821 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-09-12 08:26:00,829 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 08:26:00,834 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 08:27:01,139 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 08:27:01,145 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 08:27:01,147 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-09-12 08:27:01,157 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 08:27:01,161 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-09-12 08:27:01,163 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-09-12 08:27:01,165 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 08:27:01,167 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-09-12 08:27:01,171 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-09-12 08:27:01,175 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 08:27:01,179 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-09-12 08:27:01,182 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-09-12 08:27:01,187 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-09-12 08:27:01,191 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:27:01,193 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 08:27:01,195 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 08:27:01,210 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-09-12 08:27:01,216 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 08:27:01,222 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 08:27:01,225 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 08:27:01,228 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-09-12 08:27:01,242 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 08:27:01,249 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 08:27:01,255 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 08:27:01,257 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-09-12 08:27:01,263 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 08:27:01,267 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-09-12 08:27:01,271 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-09-12 08:27:01,278 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 08:27:01,280 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 08:27:01,282 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 08:27:01,287 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-09-12 08:27:01,289 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 08:27:01,296 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-09-12 08:27:01,304 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 08:27:01,310 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-09-12 08:27:01,312 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:27:01,313 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 08:27:01,315 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-09-12 08:28:01,993 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 08:28:01,995 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 08:28:01,997 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for working
2025-09-12 08:28:02,002 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for working
2025-09-12 08:28:02,005 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 08:28:02,009 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for working
2025-09-12 08:28:02,011 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 08:28:02,013 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for working
2025-09-12 08:28:02,015 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 08:28:02,016 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 08:28:02,018 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for working
2025-09-12 08:28:02,021 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for working
2025-09-12 08:28:02,030 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for working
2025-09-12 08:28:02,044 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for working
2025-09-12 08:28:02,050 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:28:02,059 ERROR scheduler Skipped queueing frappe.core.doctype.user_invitation.user_invitation.mark_expired_invitations because it was found in queue for working
2025-09-12 08:28:02,065 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for working
2025-09-12 08:28:02,067 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for working
2025-09-12 08:28:02,069 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 08:28:02,072 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for working
2025-09-12 08:28:02,075 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 08:28:02,086 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 08:28:02,092 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 08:28:02,096 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for working
2025-09-12 08:28:02,099 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 08:28:02,103 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for working
2025-09-12 08:28:02,105 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for working
2025-09-12 08:28:02,108 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for working
2025-09-12 08:28:02,110 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 08:28:02,116 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 08:28:02,119 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 08:28:02,124 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 08:28:02,131 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 08:28:02,133 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 08:28:02,136 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 08:28:02,137 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 08:28:02,140 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for working
2025-09-12 08:28:02,143 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 08:28:02,145 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for working
2025-09-12 08:31:03,400 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-12 08:31:03,416 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-12 08:31:03,428 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-12 08:31:03,440 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-12 08:31:03,443 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for working
2025-09-12 08:31:03,457 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for working
2025-09-12 08:31:03,466 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-12 08:31:03,472 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-12 08:31:03,476 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-12 08:31:03,488 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-12 08:31:03,494 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-12 08:31:03,497 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-12 08:31:03,513 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-12 08:37:06,582 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 08:37:06,588 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 08:37:06,607 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 08:37:06,636 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 08:37:06,641 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 08:37:06,668 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 08:38:06,871 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 08:38:06,885 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 08:38:06,888 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 08:38:06,891 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 08:38:06,896 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 08:38:06,920 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 08:39:07,452 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 08:39:07,468 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 08:39:07,516 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 08:39:07,519 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 08:39:07,530 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 08:39:07,539 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 08:40:07,763 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 08:40:07,789 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 08:40:07,824 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 08:40:07,828 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 08:40:07,831 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 08:40:07,856 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 08:41:08,056 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 08:41:08,061 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 08:41:08,089 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-12 08:41:08,094 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 08:41:08,108 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 08:41:08,139 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 08:41:08,145 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 08:42:08,879 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 08:42:08,914 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 08:42:08,928 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 08:42:08,935 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 08:42:08,942 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 08:42:08,975 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 08:43:09,235 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for working
2025-09-12 09:01:16,901 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 09:01:16,920 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 09:01:16,923 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 09:01:16,929 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 09:01:16,937 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 09:01:16,943 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 09:01:16,948 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 09:01:16,958 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 09:01:16,962 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 09:01:16,967 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 09:01:16,974 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 09:01:16,976 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 09:01:16,999 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 09:01:17,002 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 10:01:17,753 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 10:01:17,766 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 10:01:17,778 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 10:01:17,783 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 10:01:17,795 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 10:01:17,804 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 10:01:17,810 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 10:01:17,815 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 10:01:17,830 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 10:01:17,836 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 10:01:17,852 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 10:01:17,870 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 10:01:17,872 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 10:01:17,875 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 10:02:18,226 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 10:02:18,261 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 10:02:18,275 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 10:02:18,276 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 10:02:18,281 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 10:02:18,283 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 10:02:18,289 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 10:02:18,292 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 10:02:18,296 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 10:02:18,308 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 10:02:18,322 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 10:02:18,332 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 10:02:18,335 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 10:02:18,340 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 10:03:18,893 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 10:03:18,900 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 10:03:18,901 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 10:03:18,909 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 10:03:18,918 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 10:03:18,925 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 10:03:18,948 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 10:03:18,992 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 10:03:18,998 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 10:03:19,003 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 10:03:19,005 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 10:03:19,012 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 10:03:19,015 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 10:03:19,020 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 10:05:19,449 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 10:05:19,499 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 10:05:19,513 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 10:05:19,522 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 10:05:19,552 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 10:05:19,555 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 10:06:20,187 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for working
2025-09-12 10:06:20,214 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for working
2025-09-12 10:06:20,223 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for working
2025-09-12 10:06:20,264 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for working
2025-09-12 10:06:20,305 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for working
2025-09-12 10:06:20,340 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for working
2025-09-12 11:01:44,731 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 11:01:44,746 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 11:01:44,750 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 11:01:44,756 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 11:01:44,769 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 11:01:44,774 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 11:01:44,778 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 11:01:44,816 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 11:01:44,821 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 11:01:44,823 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 11:01:44,834 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 11:01:44,836 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 11:01:44,838 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 11:01:44,845 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 12:01:10,328 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 12:01:10,341 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 12:01:10,347 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 12:01:10,360 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 12:01:10,377 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 12:01:10,386 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 12:01:10,391 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 12:01:10,420 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 12:01:10,438 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 12:01:10,461 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 12:01:10,470 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 12:01:10,491 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 12:01:10,523 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 12:01:10,542 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 12:02:11,641 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 12:02:11,664 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 12:02:11,670 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 12:02:11,699 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 12:02:11,703 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 12:02:11,708 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 12:02:11,712 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 12:02:11,722 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 12:02:11,752 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 12:02:11,766 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 12:02:11,777 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 12:02:11,782 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 12:02:11,788 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 12:02:11,816 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 12:03:13,320 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 12:03:13,332 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 12:03:13,353 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 12:03:13,389 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 12:03:13,395 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 12:03:13,404 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 12:03:13,406 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 12:03:13,413 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 12:03:13,420 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 12:03:13,423 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 12:03:13,459 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 12:03:13,473 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 12:03:13,477 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 12:03:13,480 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 12:04:13,739 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 12:04:13,747 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 12:04:13,761 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 12:04:13,768 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 12:04:13,843 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 12:04:13,872 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 12:04:13,899 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 12:16:20,400 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for working
2025-09-12 12:16:20,406 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for working
2025-09-12 12:16:20,455 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for working
2025-09-12 12:16:20,462 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for working
2025-09-12 12:16:20,464 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for working
2025-09-12 12:16:20,468 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for working
2025-09-12 12:16:20,480 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for working
2025-09-12 12:16:20,489 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for working
2025-09-12 12:16:20,502 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for working
2025-09-12 12:16:20,516 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for working
2025-09-12 13:01:40,890 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 13:01:40,893 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 13:01:40,905 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 13:01:40,907 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 13:01:40,910 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 13:01:40,911 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 13:01:40,916 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 13:01:40,934 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 13:01:40,951 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 13:01:40,955 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 13:01:40,959 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 13:01:40,963 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 13:01:40,969 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 13:01:40,978 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 14:01:05,465 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 14:01:05,480 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 14:01:05,482 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 14:01:05,499 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 14:01:05,504 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 14:01:05,505 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 14:01:05,507 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 14:01:05,508 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 14:01:05,513 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 14:01:05,519 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 14:01:05,526 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 14:01:05,539 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 14:01:05,557 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 14:01:05,569 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 14:02:06,061 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for working
2025-09-12 14:02:06,066 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 14:02:06,068 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 14:02:06,073 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for working
2025-09-12 14:02:06,077 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for working
2025-09-12 14:02:06,082 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 14:02:06,088 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 14:02:06,093 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for working
2025-09-12 14:02:06,096 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 14:02:06,099 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 14:02:06,111 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 14:02:06,135 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for working
2025-09-12 14:02:06,147 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
2025-09-12 14:02:06,152 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for working
2025-09-12 14:03:06,563 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for working
2025-09-12 14:03:06,569 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for working
2025-09-12 14:03:06,577 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for working
2025-09-12 14:03:06,581 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for working
2025-09-12 14:03:06,585 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for working
2025-09-12 14:03:06,592 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for working
2025-09-12 14:03:06,597 ERROR scheduler Skipped queueing frappe.desk.utils.delete_old_exported_report_files because it was found in queue for working
2025-09-12 14:03:06,608 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for working
