// Copyright (c) 2025, <PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Bond Register Report"] = {
	"filters": [
    {"fieldname": "from_date", "label": "From Date", "fieldtype": "Date", "reqd": 1},
    {"fieldname": "to_date", "label": "To Date", "fieldtype": "Date", "reqd": 1},
    {"fieldname": "clearing_file", "label": "Clearing File", "fieldtype": "Link", "options": "Clearing File"},
    {"fieldname": "shipper", "label": "Supplier", "fieldtype": "Link", "options": "Shipper"}
  ]
};
