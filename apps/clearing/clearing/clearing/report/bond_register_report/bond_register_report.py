import frappe
from frappe.query_builder import DocType, Order


def execute(filters=None):
    filters = filters or {}

    ClearingFile = DocType("Clearing File")
    PortClearance = DocType("Port Clearance")
    TRAClearance = DocType("TRA Clearance")
    PhysicalVerification = DocType("Physical Verification")

    query = (
        frappe.qb.from_(ClearingFile)
        .left_join(PortClearance)
        .on(PortClearance.clearing_file == ClearingFile.name)
        .left_join(TRAClearance)
        .on(TRAClearance.clearing_file == ClearingFile.name)
        .left_join(PhysicalVerification)
        .on(PhysicalVerification.clearing_file == ClearingFile.name)
        .select(
            ClearingFile.name.as_("file"),
            ClearingFile.posting_date.as_("date"),
            ClearingFile.tansad_no.as_("tansad"),
            ClearingFile.awbbl_no.as_("bl_awb_no"),
            ClearingFile.arrival_date.as_("eta"),
            ClearingFile.bond_release_date.as_("bond_release_date"),
            PortClearance.ref_key.as_("bond_id"),
            PortClearance.etb.as_("etb"),
            ClearingFile.cargo_description.as_("goods"),
            ClearingFile.shipper.as_("supplier"),
            ClearingFile.notes.as_("comment"),
        )
        .orderby(ClearingFile.posting_date, order=Order.desc)
    )

    # Apply filters
    if filters.get("from_date") and filters.get("to_date"):
        query = query.where(
            (ClearingFile.posting_date >= filters["from_date"])
            & (ClearingFile.posting_date <= filters["to_date"])
        )

    if filters.get("clearing_file"):
        query = query.where(ClearingFile.name == filters["clearing_file"])

    if filters.get("shipper"):
        query = query.where(ClearingFile.shipper == filters["shipper"])

    data = query.run(as_dict=True)

    columns = [
        {
            "label": "File",
            "fieldname": "file",
            "fieldtype": "Link",
            "options": "Clearing File",
            "width": 110,
        },
        {"label": "Date", "fieldname": "date", "fieldtype": "Date", "width": 110},
        {"label": "TANSAD", "fieldname": "tansad", "fieldtype": "Data", "width": 120},
        {
            "label": "BL/AWB No",
            "fieldname": "bl_awb_no",
            "fieldtype": "Data",
            "width": 110,
        },
        {"label": "ETA", "fieldname": "eta", "fieldtype": "Date", "width": 110},
        {
            "label": "Bond Release Date",
            "fieldname": "bond_release_date",
            "fieldtype": "Date",
            "width": 120,
        },
        {"label": "Bond ID", "fieldname": "bond_id", "fieldtype": "Data", "width": 120},
        {"label": "ETB", "fieldname": "etb", "fieldtype": "Date", "width": 110},
        {
            "label": "Goods",
            "fieldname": "goods",
            "fieldtype": "Small Text",
            "width": 250,
        },
        {
            "label": "Supplier",
            "fieldname": "supplier",
            "fieldtype": "Link",
            "options": "Shipper",
            "width": 150,
        },
        {"label": "Comment", "fieldname": "comment", "fieldtype": "Text", "width": 250},
    ]

    return columns, data
