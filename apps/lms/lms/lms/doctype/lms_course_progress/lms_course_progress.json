{"actions": [], "creation": "2021-05-31 17:20:13.388453", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["member", "member_name", "status", "column_break_3", "lesson", "chapter", "course", "section_break_uoob", "is_scorm_chapter", "column_break_wskp", "scorm_content"], "fields": [{"fetch_from": "chapter.course", "fieldname": "course", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Course", "options": "LMS Course", "read_only": 1, "search_index": 1}, {"fetch_from": "lesson.chapter", "fieldname": "chapter", "fieldtype": "Link", "label": "Chapter", "options": "Course Chapter", "read_only": 1, "search_index": 1}, {"fieldname": "lesson", "fieldtype": "Link", "in_list_view": 1, "label": "Lesson", "options": "Course Lesson", "search_index": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Complete\nPartially Complete\nIncomplete", "search_index": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "member", "fieldtype": "Link", "label": "Member", "options": "User", "search_index": 1}, {"fetch_from": "member.full_name", "fieldname": "member_name", "fieldtype": "Data", "label": "Member Name", "read_only": 1}, {"fieldname": "section_break_uoob", "fieldtype": "Section Break"}, {"default": "0", "fetch_from": "chapter.is_scorm_package", "fetch_if_empty": 1, "fieldname": "is_scorm_chapter", "fieldtype": "Check", "label": "Is SCORM Chapter", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_wskp", "fieldtype": "Column Break"}, {"depends_on": "eval: doc.is_scorm_chapter == 1 && doc.status == 'Partially Complete'", "fieldname": "scorm_content", "fieldtype": "Long Text", "label": "SCORM Content", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-01-20 22:25:36.829929", "modified_by": "Administrator", "module": "LMS", "name": "LMS Course Progress", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "LMS Student", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "member_name", "track_changes": 1}