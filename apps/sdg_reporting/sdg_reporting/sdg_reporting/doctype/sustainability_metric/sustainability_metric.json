{"actions": [], "allow_rename": 1, "autoname": "field:metric_name", "creation": "2025-05-14 09:43:54.065347", "doctype": "DocType", "engine": "InnoDB", "field_order": ["metric_name", "esg_pillar", "gri_disclosure", "sdg_link", "sdg_target", "column_break_hlgl", "measures", "unit", "q1", "q2"], "fields": [{"fieldname": "column_break_hlgl", "fieldtype": "Column Break"}, {"fieldname": "unit", "fieldtype": "Data", "label": "Unit"}, {"fieldname": "metric_name", "fieldtype": "Data", "label": "Metric Name", "options": "SDG Indicator", "unique": 1}, {"fieldname": "esg_pillar", "fieldtype": "Link", "label": "ESG Pillar", "options": "ESG Criterion"}, {"fieldname": "gri_disclosure", "fieldtype": "Link", "label": "GRI Disclosure", "options": "GRI Standard"}, {"fieldname": "sdg_link", "fieldtype": "Link", "label": "SDG Link", "options": "SDG Goal"}, {"fieldname": "sdg_target", "fieldtype": "Link", "label": "SDG Target", "options": "SDG Target"}, {"fieldname": "measures", "fieldtype": "Data", "label": "Measures"}, {"fieldname": "q1", "fieldtype": "Data", "label": "Q1"}, {"fieldname": "q2", "fieldtype": "Data", "label": "Q2"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-17 12:47:55.145617", "modified_by": "Administrator", "module": "Sdg Reporting", "name": "Sustainability Metric", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}