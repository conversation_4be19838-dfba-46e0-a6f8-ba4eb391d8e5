#!/usr/bin/env python3
# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

"""
<PERSON><PERSON><PERSON> to setup SDG dashboards manually
Run this script from bench console:

bench --site [site-name] console
>>> exec(open('apps/sdg_reporting/sdg_reporting/sdg_reporting/setup_dashboards.py').read())
"""

import frappe
from sdg_reporting.sdg_reporting.setup.install import setup_sdg_dashboards, setup_sample_data

def main():
    """Main function to setup SDG dashboards"""
    print("Setting up SDG Dashboards...")
    
    # Setup dashboards
    setup_sdg_dashboards()
    
    # Setup sample data
    setup_sample_data()
    
    print("SDG Dashboard setup completed!")
    print("\nYou can now access the dashboards at:")
    print("- SDG Overview: /app/dashboard-view/SDG%20Overview")
    print("- SDG Health Metrics: /app/dashboard-view/SDG%20Health%20Metrics") 
    print("- ESG Mapping: /app/dashboard-view/ESG%20Mapping")

if __name__ == "__main__":
    main()
