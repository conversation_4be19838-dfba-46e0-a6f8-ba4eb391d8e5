#!/usr/bin/env python3
# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

"""
Simple script to create SDG dashboards manually
Run this script from bench console:

bench --site [site-name] console
>>> exec(open('apps/sdg_reporting/sdg_reporting/sdg_reporting/create_simple_dashboards.py').read())
"""

import frappe
import json

def create_simple_dashboards():
    """Create simple SDG dashboards that work"""
    print("Creating Simple SDG Dashboards...")
    
    # Create Number Cards first
    create_simple_number_cards()
    
    # Create Dashboard Charts
    create_simple_dashboard_charts()
    
    # Create Dashboards
    create_simple_dashboard_configs()
    
    # Create sample data
    create_sample_data()
    
    frappe.db.commit()
    print("Simple SDG Dashboards created successfully!")
    print("\nAccess dashboards at:")
    print("- SDG Overview: /app/dashboard-view/SDG%20Overview")


def create_simple_number_cards():
    """Create simple number cards"""
    cards = [
        {
            "name": "Total SDG Goals",
            "label": "Total SDG Goals",
            "type": "Document Type",
            "document_type": "SDG Goal",
            "function": "Count",
            "color": "#2E86AB",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        {
            "name": "Active Sustainability Metrics",
            "label": "Active Sustainability Metrics",
            "type": "Document Type",
            "document_type": "Sustainability Metric",
            "function": "Count",
            "color": "#A23B72",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        {
            "name": "Total Sustainability Entries",
            "label": "Total Sustainability Entries",
            "type": "Document Type",
            "document_type": "Sustainability Entry",
            "function": "Count",
            "color": "#F18F01",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        {
            "name": "Total ESG Criteria",
            "label": "Total ESG Criteria",
            "type": "Document Type",
            "document_type": "ESG Criterion",
            "function": "Count",
            "color": "#C73E1D",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
    ]
    
    for card_data in cards:
        if not frappe.db.exists("Number Card", card_data["name"]):
            try:
                doc = frappe.new_doc("Number Card")
                doc.update(card_data)
                doc.insert(ignore_permissions=True)
                print(f"Created Number Card: {card_data['name']}")
            except Exception as e:
                print(f"Error creating Number Card {card_data['name']}: {str(e)}")


def create_simple_dashboard_charts():
    """Create simple dashboard charts"""
    charts = [
        {
            "name": "SDG Metrics Count",
            "chart_name": "SDG Metrics Count",
            "chart_type": "Count",
            "document_type": "Sustainability Metric",
            "based_on": "creation",
            "timeseries": 1,
            "time_interval": "Monthly",
            "timespan": "Last Year",
            "type": "Line",
            "color": "#2E86AB",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        {
            "name": "Sustainability Entries Trend",
            "chart_name": "Sustainability Entries Trend",
            "chart_type": "Count",
            "document_type": "Sustainability Entry",
            "based_on": "creation",
            "timeseries": 1,
            "time_interval": "Monthly",
            "timespan": "Last Year",
            "type": "Line",
            "color": "#A23B72",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
    ]
    
    for chart_data in charts:
        if not frappe.db.exists("Dashboard Chart", chart_data["name"]):
            try:
                doc = frappe.new_doc("Dashboard Chart")
                doc.update(chart_data)
                doc.insert(ignore_permissions=True)
                print(f"Created Dashboard Chart: {chart_data['name']}")
            except Exception as e:
                print(f"Error creating Dashboard Chart {chart_data['name']}: {str(e)}")


def create_simple_dashboard_configs():
    """Create simple dashboard configurations"""
    dashboards = [
        {
            "name": "SDG Overview",
            "dashboard_name": "SDG Overview",
            "module": "Sdg Reporting",
            "is_standard": 1,
            "charts": [
                {"chart": "SDG Metrics Count", "width": "Half"},
                {"chart": "Sustainability Entries Trend", "width": "Half"},
            ],
            "cards": [
                {"card": "Total SDG Goals"},
                {"card": "Active Sustainability Metrics"},
                {"card": "Total Sustainability Entries"},
                {"card": "Total ESG Criteria"},
            ],
        },
    ]
    
    for dashboard_data in dashboards:
        if not frappe.db.exists("Dashboard", dashboard_data["name"]):
            try:
                doc = frappe.new_doc("Dashboard")
                doc.dashboard_name = dashboard_data["dashboard_name"]
                doc.module = dashboard_data["module"]
                doc.is_standard = dashboard_data.get("is_standard", 1)
                
                # Add charts
                for chart_link in dashboard_data.get("charts", []):
                    doc.append("charts", {
                        "chart": chart_link["chart"],
                        "width": chart_link.get("width", "Half")
                    })
                
                # Add cards
                for card_link in dashboard_data.get("cards", []):
                    doc.append("cards", {
                        "card": card_link["card"]
                    })
                
                doc.insert(ignore_permissions=True)
                print(f"Created Dashboard: {dashboard_data['name']}")
            except Exception as e:
                print(f"Error creating Dashboard {dashboard_data['name']}: {str(e)}")


def create_sample_data():
    """Create sample data for demonstration"""
    try:
        # Create sample SDG Goals
        sample_goals = [
            {"goal_no": "1", "goal_name": "No Poverty", "description": "End poverty in all its forms everywhere"},
            {"goal_no": "2", "goal_name": "Zero Hunger", "description": "End hunger, achieve food security and improved nutrition"},
            {"goal_no": "3", "goal_name": "Good Health and Well-being", "description": "Ensure healthy lives and promote well-being for all"},
            {"goal_no": "4", "goal_name": "Quality Education", "description": "Ensure inclusive and equitable quality education"},
            {"goal_no": "5", "goal_name": "Gender Equality", "description": "Achieve gender equality and empower all women and girls"},
        ]
        
        for goal_data in sample_goals:
            if not frappe.db.exists("SDG Goal", goal_data["goal_no"]):
                doc = frappe.new_doc("SDG Goal")
                doc.update(goal_data)
                doc.insert(ignore_permissions=True)
                print(f"Created SDG Goal: {goal_data['goal_no']}")
        
        # Create sample ESG Criteria
        sample_esg = [
            {"criterion_name": "Carbon Emissions", "pillar": "Environmental", "description": "Greenhouse gas emissions measurement"},
            {"criterion_name": "Water Usage", "pillar": "Environmental", "description": "Water consumption and conservation"},
            {"criterion_name": "Employee Diversity", "pillar": "Social", "description": "Workforce diversity and inclusion"},
            {"criterion_name": "Community Impact", "pillar": "Social", "description": "Local community engagement and support"},
            {"criterion_name": "Board Independence", "pillar": "Governance", "description": "Independent board members percentage"},
            {"criterion_name": "Ethics Training", "pillar": "Governance", "description": "Ethics and compliance training programs"},
        ]
        
        for esg_data in sample_esg:
            if not frappe.db.exists("ESG Criterion", esg_data["criterion_name"]):
                doc = frappe.new_doc("ESG Criterion")
                doc.update(esg_data)
                doc.insert(ignore_permissions=True)
                print(f"Created ESG Criterion: {esg_data['criterion_name']}")
        
        # Create sample Sustainability Metrics
        sample_metrics = [
            {
                "metric_name": "Maternal Mortality Rate",
                "esg_pillar": "Social",
                "sdg_link": "3",
                "unit": "per 100,000 live births",
                "measures": "Health outcomes for mothers"
            },
            {
                "metric_name": "Healthcare Coverage",
                "esg_pillar": "Social", 
                "sdg_link": "3",
                "unit": "percentage",
                "measures": "Population with health insurance"
            },
            {
                "metric_name": "Carbon Footprint",
                "esg_pillar": "Environmental",
                "sdg_link": "13",
                "unit": "tonnes CO2e",
                "measures": "Total greenhouse gas emissions"
            },
            {
                "metric_name": "Education Access",
                "esg_pillar": "Social",
                "sdg_link": "4", 
                "unit": "percentage",
                "measures": "Children with access to quality education"
            },
        ]
        
        for metric_data in sample_metrics:
            if not frappe.db.exists("Sustainability Metric", metric_data["metric_name"]):
                doc = frappe.new_doc("Sustainability Metric")
                doc.update(metric_data)
                doc.insert(ignore_permissions=True)
                print(f"Created Sustainability Metric: {metric_data['metric_name']}")
        
        print("Sample data created successfully!")
        
    except Exception as e:
        print(f"Error creating sample data: {str(e)}")


if __name__ == "__main__":
    create_simple_dashboards()
