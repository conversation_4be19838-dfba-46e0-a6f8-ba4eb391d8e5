#!/usr/bin/env python3
# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

"""
Test script for SDG dashboards
Run this from bench console to verify dashboard setup:

bench --site [site-name] console
>>> exec(open('apps/sdg_reporting/sdg_reporting/sdg_reporting/test_dashboards.py').read())
"""

import frappe

def test_dashboard_setup():
    """Test if SDG dashboards are properly set up"""
    print("Testing SDG Dashboard Setup...")
    print("=" * 50)
    
    # Test Dashboard Chart Sources
    print("\n1. Testing Dashboard Chart Sources:")
    chart_sources = [
        "SDG Progress Analysis",
        "Health Indicators Analysis", 
        "ESG SDG Mapping Analysis"
    ]
    
    for source in chart_sources:
        exists = frappe.db.exists("Dashboard Chart Source", source)
        status = "✓ EXISTS" if exists else "✗ MISSING"
        print(f"   {source}: {status}")
    
    # Test Number Cards
    print("\n2. Testing Number Cards:")
    number_cards = [
        "Total SDG Goals",
        "Active Sustainability Metrics",
        "Total Sustainability Entries",
        "ESG Alignment Percentage",
        "Health Metrics Count",
        "Latest Health Score"
    ]
    
    for card in number_cards:
        exists = frappe.db.exists("Number Card", card)
        status = "✓ EXISTS" if exists else "✗ MISSING"
        print(f"   {card}: {status}")
    
    # Test Dashboard Charts
    print("\n3. Testing Dashboard Charts:")
    dashboard_charts = [
        "SDG Goal Distribution",
        "ESG Pillar Distribution", 
        "Sustainability Metrics Trend",
        "SDG Progress by Goal",
        "Health Metrics Trend",
        "Health Indicators Comparison"
    ]
    
    for chart in dashboard_charts:
        exists = frappe.db.exists("Dashboard Chart", chart)
        status = "✓ EXISTS" if exists else "✗ MISSING"
        print(f"   {chart}: {status}")
    
    # Test Dashboards
    print("\n4. Testing Dashboards:")
    dashboards = [
        "SDG Overview",
        "SDG Health Metrics",
        "ESG Mapping"
    ]
    
    for dashboard in dashboards:
        exists = frappe.db.exists("Dashboard", dashboard)
        status = "✓ EXISTS" if exists else "✗ MISSING"
        print(f"   {dashboard}: {status}")
        
        if exists:
            # Get dashboard details
            doc = frappe.get_doc("Dashboard", dashboard)
            print(f"     - Charts: {len(doc.charts)}")
            print(f"     - Cards: {len(doc.cards)}")
    
    # Test Sample Data
    print("\n5. Testing Sample Data:")
    
    # SDG Goals
    goal_count = frappe.db.count("SDG Goal")
    print(f"   SDG Goals: {goal_count}")
    
    # Sustainability Metrics
    metric_count = frappe.db.count("Sustainability Metric")
    print(f"   Sustainability Metrics: {metric_count}")
    
    # ESG Criteria
    esg_count = frappe.db.count("ESG Criterion")
    print(f"   ESG Criteria: {esg_count}")
    
    # Sustainability Entries
    entry_count = frappe.db.count("Sustainability Entry")
    print(f"   Sustainability Entries: {entry_count}")
    
    # Test API Methods
    print("\n6. Testing API Methods:")
    try:
        from sdg_reporting.sdg_reporting.api.dashboard import (
            get_esg_alignment_percentage,
            get_latest_health_score,
            get_health_target_achievement
        )
        
        esg_alignment = get_esg_alignment_percentage()
        print(f"   ESG Alignment: {esg_alignment}%")
        
        health_score = get_latest_health_score()
        print(f"   Latest Health Score: {health_score}")
        
        health_achievement = get_health_target_achievement()
        print(f"   Health Target Achievement: {health_achievement}%")
        
    except Exception as e:
        print(f"   ✗ API Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("Dashboard URLs:")
    print("- SDG Overview: /app/dashboard-view/SDG%20Overview")
    print("- SDG Health Metrics: /app/dashboard-view/SDG%20Health%20Metrics")
    print("- ESG Mapping: /app/dashboard-view/ESG%20Mapping")
    print("=" * 50)


def create_sample_entries():
    """Create sample sustainability entries for testing"""
    print("\nCreating sample sustainability entries...")
    
    # Get existing metrics
    metrics = frappe.get_all("Sustainability Metric", fields=["name", "metric_name"])
    
    if not metrics:
        print("No sustainability metrics found. Please create some first.")
        return
    
    import random
    from datetime import datetime, timedelta
    
    # Create entries for the last 6 months
    for i in range(6):
        date = datetime.now() - timedelta(days=30 * i)
        
        for metric in metrics[:3]:  # Create entries for first 3 metrics
            entry_name = f"{metric.name}-{date.strftime('%Y-%m')}"
            
            if not frappe.db.exists("Sustainability Entry", {"metric": metric.name, "reporting_period": date.date()}):
                doc = frappe.new_doc("Sustainability Entry")
                doc.metric = metric.name
                doc.reporting_period = date.date()
                doc.value = random.uniform(50, 100)  # Random value between 50-100
                doc.remarks = f"Sample data for {metric.metric_name}"
                doc.insert(ignore_permissions=True)
    
    frappe.db.commit()
    print(f"Created sample entries for {len(metrics[:3])} metrics over 6 months")


if __name__ == "__main__":
    test_dashboard_setup()
    
    # Optionally create sample entries
    create_sample = input("\nCreate sample sustainability entries? (y/n): ")
    if create_sample.lower() == 'y':
        create_sample_entries()
