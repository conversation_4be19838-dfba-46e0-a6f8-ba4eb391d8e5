# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt


@frappe.whitelist()
def get_data(
    chart_name=None,
    chart=None,
    no_cache=None,
    filters=None,
    from_date=None,
    to_date=None,
    timespan=None,
    time_interval=None,
    heatmap_year=None,
):
    """Get ESG to SDG mapping analysis data for heatmap chart"""
    
    if filters:
        filters = frappe.parse_json(filters)
    
    # Define ESG pillars
    esg_pillars = ["Environmental", "Social", "Governance"]
    
    # Get SDG goals (1-17)
    sdg_goals = list(range(1, 18))
    
    # Create heatmap data
    heatmap_data = []
    
    for i, pillar in enumerate(esg_pillars):
        for j, goal in enumerate(sdg_goals):
            # Calculate alignment score between ESG pillar and SDG goal
            alignment_score = calculate_esg_sdg_alignment(pillar, str(goal))
            
            heatmap_data.append({
                "x": j,  # SDG goal index
                "y": i,  # ESG pillar index
                "v": alignment_score  # alignment value
            })
    
    return {
        "labels": [f"SDG {goal}" for goal in sdg_goals],
        "yLabels": esg_pillars,
        "datasets": [
            {
                "name": "ESG-SDG Alignment",
                "values": heatmap_data,
                "chartType": "heatmap"
            }
        ]
    }


def calculate_esg_sdg_alignment(esg_pillar, sdg_goal):
    """Calculate alignment score between ESG pillar and SDG goal"""
    try:
        # Count metrics that link this ESG pillar to this SDG goal
        metrics_count = frappe.db.count(
            "Sustainability Metric",
            filters={
                "esg_pillar": ["like", f"%{esg_pillar}%"],
                "sdg_link": sdg_goal
            }
        )
        
        # Get total metrics for this ESG pillar
        total_esg_metrics = frappe.db.count(
            "Sustainability Metric",
            filters={"esg_pillar": ["like", f"%{esg_pillar}%"]}
        )
        
        if total_esg_metrics == 0:
            return 0
        
        # Calculate alignment as percentage
        alignment = (metrics_count / total_esg_metrics) * 100
        return flt(alignment, 1)
        
    except Exception as e:
        frappe.log_error(f"Error calculating ESG-SDG alignment for {esg_pillar}-{sdg_goal}: {str(e)}")
        return 0
