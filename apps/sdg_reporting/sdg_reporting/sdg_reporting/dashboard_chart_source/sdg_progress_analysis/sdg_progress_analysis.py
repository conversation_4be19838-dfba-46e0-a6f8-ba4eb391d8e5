# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt


@frappe.whitelist()
def get_data(
    chart_name=None,
    chart=None,
    no_cache=None,
    filters=None,
    from_date=None,
    to_date=None,
    timespan=None,
    time_interval=None,
    heatmap_year=None,
):
    """Get SDG progress analysis data for dashboard chart"""
    
    if filters:
        filters = frappe.parse_json(filters)
    
    # Get all SDG goals
    sdg_goals = frappe.get_all(
        "SDG Goal",
        fields=["goal_no", "goal_name"],
        order_by="goal_no"
    )
    
    labels = []
    values = []
    
    for goal in sdg_goals:
        # Calculate progress for each goal
        progress = calculate_goal_progress(goal.goal_no)
        labels.append(f"SDG {goal.goal_no}")
        values.append(progress)
    
    return {
        "labels": labels,
        "datasets": [
            {
                "name": "Progress %",
                "values": values,
                "chartType": "bar"
            }
        ]
    }


def calculate_goal_progress(goal_no):
    """Calculate progress percentage for a specific SDG goal"""
    try:
        # Get metrics linked to this goal
        goal_metrics = frappe.get_all(
            "Sustainability Metric",
            filters={"sdg_link": goal_no},
            fields=["name"]
        )
        
        if not goal_metrics:
            return 0
        
        # Count metrics with recent data entries
        metrics_with_data = 0
        for metric in goal_metrics:
            if frappe.db.exists("Sustainability Entry", {"metric": metric.name}):
                metrics_with_data += 1
        
        # Calculate percentage
        progress = (metrics_with_data / len(goal_metrics)) * 100
        return flt(progress, 1)
        
    except Exception as e:
        frappe.log_error(f"Error calculating goal progress for SDG {goal_no}: {str(e)}")
        return 0
