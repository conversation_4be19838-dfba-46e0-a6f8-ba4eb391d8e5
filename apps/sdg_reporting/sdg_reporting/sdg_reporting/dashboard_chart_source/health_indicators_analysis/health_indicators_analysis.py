# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt


@frappe.whitelist()
def get_data(
    chart_name=None,
    chart=None,
    no_cache=None,
    filters=None,
    from_date=None,
    to_date=None,
    timespan=None,
    time_interval=None,
    heatmap_year=None,
):
    """Get health indicators analysis data for dashboard chart"""
    
    if filters:
        filters = frappe.parse_json(filters)
    
    # Define key health indicators
    health_indicators = [
        "Maternal Mortality Rate",
        "Under-5 Mortality Rate", 
        "Healthcare Coverage",
        "Disease Prevention Rate",
        "Mental Health Support",
        "Vaccination Coverage"
    ]
    
    labels = []
    current_values = []
    target_values = []
    
    for indicator in health_indicators:
        # Get latest value for this indicator
        current_value = get_latest_indicator_value(indicator)
        target_value = get_target_value(indicator)
        
        labels.append(indicator)
        current_values.append(current_value)
        target_values.append(target_value)
    
    return {
        "labels": labels,
        "datasets": [
            {
                "name": "Current Value",
                "values": current_values,
                "chartType": "bar"
            },
            {
                "name": "Target Value", 
                "values": target_values,
                "chartType": "bar"
            }
        ]
    }


def get_latest_indicator_value(indicator_name):
    """Get the latest value for a health indicator"""
    try:
        # Find metrics that match this indicator
        metrics = frappe.get_all(
            "Sustainability Metric",
            filters={
                "metric_name": ["like", f"%{indicator_name}%"],
                "sdg_link": "3"
            },
            fields=["name"]
        )
        
        if not metrics:
            return 0
        
        # Get latest entry for this metric
        latest_entry = frappe.get_all(
            "Sustainability Entry",
            filters={"metric": ["in", [m.name for m in metrics]]},
            fields=["value"],
            order_by="reporting_period desc",
            limit=1
        )
        
        return flt(latest_entry[0].value) if latest_entry else 0
        
    except Exception as e:
        frappe.log_error(f"Error getting indicator value for {indicator_name}: {str(e)}")
        return 0


def get_target_value(indicator_name):
    """Get target value for a health indicator (placeholder implementation)"""
    # This would typically come from SDG targets or organizational goals
    target_map = {
        "Maternal Mortality Rate": 70,  # per 100,000 live births
        "Under-5 Mortality Rate": 25,   # per 1,000 live births
        "Healthcare Coverage": 100,      # percentage
        "Disease Prevention Rate": 95,   # percentage
        "Mental Health Support": 80,     # percentage
        "Vaccination Coverage": 95       # percentage
    }
    
    return target_map.get(indicator_name, 100)
