# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from sdg_reporting.sdg_reporting.dashboard_fixtures import (
    get_dashboards, 
    get_dashboard_charts, 
    get_number_cards
)


def after_install():
    """Setup SDG dashboards after app installation"""
    setup_sdg_dashboards()


def setup_sdg_dashboards():
    """Create SDG dashboards, charts, and number cards"""
    try:
        # Create dashboard chart sources first
        create_dashboard_chart_sources()
        
        # Create number cards
        create_number_cards()
        
        # Create dashboard charts
        create_dashboard_charts()
        
        # Create dashboards
        create_dashboards()
        
        frappe.db.commit()
        print("SDG Dashboards setup completed successfully!")
        
    except Exception as e:
        frappe.log_error(f"Error setting up SDG dashboards: {str(e)}")
        print(f"Error setting up SDG dashboards: {str(e)}")


def create_dashboard_chart_sources():
    """Create dashboard chart sources"""
    chart_sources = [
        {
            "source_name": "SDG Progress Analysis",
            "module": "Sdg Reporting",
            "timeseries": 0
        },
        {
            "source_name": "Health Indicators Analysis",
            "module": "Sdg Reporting", 
            "timeseries": 0
        },
        {
            "source_name": "ESG SDG Mapping Analysis",
            "module": "Sdg Reporting",
            "timeseries": 0
        }
    ]
    
    for source_data in chart_sources:
        if not frappe.db.exists("Dashboard Chart Source", source_data["source_name"]):
            doc = frappe.new_doc("Dashboard Chart Source")
            doc.update(source_data)
            doc.insert(ignore_permissions=True)
            print(f"Created Dashboard Chart Source: {source_data['source_name']}")


def create_number_cards():
    """Create number cards for dashboards"""
    number_cards = get_number_cards()
    
    for card_data in number_cards:
        if not frappe.db.exists("Number Card", card_data["name"]):
            doc = frappe.new_doc("Number Card")
            doc.update(card_data)
            doc.insert(ignore_permissions=True)
            print(f"Created Number Card: {card_data['name']}")


def create_dashboard_charts():
    """Create dashboard charts"""
    dashboard_charts = get_dashboard_charts()
    
    for chart_data in dashboard_charts:
        if not frappe.db.exists("Dashboard Chart", chart_data["name"]):
            doc = frappe.new_doc("Dashboard Chart")
            doc.update(chart_data)
            doc.insert(ignore_permissions=True)
            print(f"Created Dashboard Chart: {chart_data['name']}")


def create_dashboards():
    """Create dashboards with charts and cards"""
    dashboards = get_dashboards()
    
    for dashboard_data in dashboards:
        if not frappe.db.exists("Dashboard", dashboard_data["name"]):
            doc = frappe.new_doc("Dashboard")
            doc.dashboard_name = dashboard_data["dashboard_name"]
            doc.module = "Sdg Reporting"
            doc.is_standard = 1
            
            # Add charts
            for chart_link in dashboard_data.get("charts", []):
                doc.append("charts", {
                    "chart": chart_link["chart"],
                    "width": chart_link.get("width", "Half")
                })
            
            # Add cards
            for card_link in dashboard_data.get("cards", []):
                doc.append("cards", {
                    "card": card_link["card"]
                })
            
            doc.insert(ignore_permissions=True)
            print(f"Created Dashboard: {dashboard_data['name']}")


def setup_sample_data():
    """Create sample SDG data for demonstration"""
    try:
        # Create sample SDG Goals if they don't exist
        sample_goals = [
            {"goal_no": "1", "goal_name": "No Poverty", "description": "End poverty in all its forms everywhere"},
            {"goal_no": "2", "goal_name": "Zero Hunger", "description": "End hunger, achieve food security and improved nutrition"},
            {"goal_no": "3", "goal_name": "Good Health and Well-being", "description": "Ensure healthy lives and promote well-being for all"},
            {"goal_no": "4", "goal_name": "Quality Education", "description": "Ensure inclusive and equitable quality education"},
            {"goal_no": "5", "goal_name": "Gender Equality", "description": "Achieve gender equality and empower all women and girls"},
        ]
        
        for goal_data in sample_goals:
            if not frappe.db.exists("SDG Goal", goal_data["goal_no"]):
                doc = frappe.new_doc("SDG Goal")
                doc.update(goal_data)
                doc.insert(ignore_permissions=True)
        
        # Create sample ESG Criteria
        sample_esg = [
            {"criterion_name": "Carbon Emissions", "pillar": "Environmental", "description": "Greenhouse gas emissions measurement"},
            {"criterion_name": "Water Usage", "pillar": "Environmental", "description": "Water consumption and conservation"},
            {"criterion_name": "Employee Diversity", "pillar": "Social", "description": "Workforce diversity and inclusion"},
            {"criterion_name": "Community Impact", "pillar": "Social", "description": "Local community engagement and support"},
            {"criterion_name": "Board Independence", "pillar": "Governance", "description": "Independent board members percentage"},
            {"criterion_name": "Ethics Training", "pillar": "Governance", "description": "Ethics and compliance training programs"},
        ]
        
        for esg_data in sample_esg:
            if not frappe.db.exists("ESG Criterion", esg_data["criterion_name"]):
                doc = frappe.new_doc("ESG Criterion")
                doc.update(esg_data)
                doc.insert(ignore_permissions=True)
        
        # Create sample Sustainability Metrics
        sample_metrics = [
            {
                "metric_name": "Maternal Mortality Rate",
                "esg_pillar": "Social",
                "sdg_link": "3",
                "unit": "per 100,000 live births",
                "measures": "Health outcomes for mothers"
            },
            {
                "metric_name": "Healthcare Coverage",
                "esg_pillar": "Social", 
                "sdg_link": "3",
                "unit": "percentage",
                "measures": "Population with health insurance"
            },
            {
                "metric_name": "Carbon Footprint",
                "esg_pillar": "Environmental",
                "sdg_link": "13",
                "unit": "tonnes CO2e",
                "measures": "Total greenhouse gas emissions"
            },
            {
                "metric_name": "Education Access",
                "esg_pillar": "Social",
                "sdg_link": "4", 
                "unit": "percentage",
                "measures": "Children with access to quality education"
            },
        ]
        
        for metric_data in sample_metrics:
            if not frappe.db.exists("Sustainability Metric", metric_data["metric_name"]):
                doc = frappe.new_doc("Sustainability Metric")
                doc.update(metric_data)
                doc.insert(ignore_permissions=True)
        
        frappe.db.commit()
        print("Sample SDG data created successfully!")
        
    except Exception as e:
        frappe.log_error(f"Error creating sample data: {str(e)}")
        print(f"Error creating sample data: {str(e)}")
