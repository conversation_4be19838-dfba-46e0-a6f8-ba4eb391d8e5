# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt, getdate, today, add_months, get_datetime
import json
from datetime import datetime, timedelta


# Number Card Methods
@frappe.whitelist()
def get_esg_alignment_percentage():
    """Get ESG alignment percentage for number card"""
    try:
        total_metrics = frappe.db.count('Sustainability Metric')
        if total_metrics == 0:
            return 0
        
        metrics_with_esg = frappe.db.count('Sustainability Metric', {'esg_pillar': ['!=', '']})
        return round((metrics_with_esg / total_metrics) * 100, 1)
    except:
        return 0


@frappe.whitelist()
def get_latest_health_score():
    """Get latest health score for number card"""
    try:
        # Get latest health-related entries
        health_entries = frappe.db.sql("""
            SELECT AVG(se.value) as avg_score
            FROM `tabSustainability Entry` se
            JOIN `tabSustainability Metric` sm ON se.metric = sm.name
            WHERE sm.sdg_link = '3'
            AND se.reporting_period >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
        """)
        
        return round(health_entries[0][0], 1) if health_entries and health_entries[0][0] else 0
    except:
        return 0


@frappe.whitelist()
def get_health_target_achievement():
    """Get health target achievement percentage for number card"""
    try:
        # Calculate based on health metrics with targets
        health_metrics = frappe.get_all(
            'Sustainability Metric',
            filters={'sdg_link': '3'},
            fields=['name', 'q1', 'q2']  # Assuming q1/q2 contain target values
        )
        
        if not health_metrics:
            return 0
        
        achievement_count = 0
        total_with_targets = 0
        
        for metric in health_metrics:
            if metric.q1 or metric.q2:  # Has target
                total_with_targets += 1
                # Check if latest entry meets target (simplified logic)
                latest_entry = frappe.get_all(
                    'Sustainability Entry',
                    filters={'metric': metric.name},
                    fields=['value'],
                    order_by='reporting_period desc',
                    limit=1
                )
                
                if latest_entry:
                    target = flt(metric.q1) or flt(metric.q2) or 100
                    if latest_entry[0].value >= target * 0.8:  # 80% of target considered achievement
                        achievement_count += 1
        
        if total_with_targets == 0:
            return 0
        
        return round((achievement_count / total_with_targets) * 100, 1)
    except:
        return 0
