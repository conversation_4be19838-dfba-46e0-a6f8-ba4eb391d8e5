# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import json
import frappe
from frappe import _


def get_dashboards():
    """Return dashboard configurations for SDG Reporting"""
    return [
        {
            "name": "SDG Overview",
            "dashboard_name": "SDG Overview",
            "charts": [
                {"chart": "SDG Goal Distribution", "width": "Half"},
                {"chart": "ESG Pillar Distribution", "width": "Half"},
                {"chart": "Sustainability Metrics Trend", "width": "Full"},
                {"chart": "SDG Progress by Goal", "width": "Full"},
            ],
            "cards": [
                {"card": "Total SDG Goals"},
                {"card": "Active Sustainability Metrics"},
                {"card": "Total Sustainability Entries"},
                {"card": "ESG Alignment Percentage"},
            ],
        },
        {
            "name": "SDG Health Metrics",
            "dashboard_name": "SDG Health Metrics",
            "charts": [
                {"chart": "Health Metrics Trend", "width": "Full"},
                {"chart": "Health Indicators Comparison", "width": "Half"},
                {"chart": "Health Coverage Analysis", "width": "Half"},
            ],
            "cards": [
                {"card": "Health Metrics Count"},
                {"card": "Latest Health Score"},
                {"card": "Health Entries This Month"},
                {"card": "Health Target Achievement"},
            ],
        },
        {
            "name": "ESG Mapping",
            "dashboard_name": "ESG Mapping",
            "charts": [
                {"chart": "ESG to SDG Mapping", "width": "Full"},
                {"chart": "Environmental Metrics", "width": "Half"},
                {"chart": "Social Metrics", "width": "Half"},
                {"chart": "Governance Metrics", "width": "Full"},
            ],
            "cards": [
                {"card": "Environmental Metrics Count"},
                {"card": "Social Metrics Count"},
                {"card": "Governance Metrics Count"},
                {"card": "Total ESG Criteria"},
            ],
        },
    ]


def get_dashboard_charts():
    """Return dashboard chart configurations"""
    return [
        # SDG Goal Distribution Chart
        {
            "doctype": "Dashboard Chart",
            "name": "SDG Goal Distribution",
            "chart_name": "SDG Goal Distribution",
            "chart_type": "Group By",
            "document_type": "Sustainability Metric",
            "based_on": "sdg_link",
            "value_based_on": "name",
            "group_by_type": "Count",
            "group_by_based_on": "sdg_link",
            "type": "Donut",
            "color": "#2E86AB",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        # ESG Pillar Distribution Chart
        {
            "doctype": "Dashboard Chart",
            "name": "ESG Pillar Distribution",
            "chart_name": "ESG Pillar Distribution",
            "chart_type": "Group By",
            "document_type": "Sustainability Metric",
            "based_on": "esg_pillar",
            "value_based_on": "name",
            "group_by_type": "Count",
            "group_by_based_on": "esg_pillar",
            "type": "Bar",
            "color": "#A23B72",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        # Sustainability Metrics Trend
        {
            "doctype": "Dashboard Chart",
            "name": "Sustainability Metrics Trend",
            "chart_name": "Sustainability Metrics Trend",
            "chart_type": "Count",
            "document_type": "Sustainability Entry",
            "based_on": "reporting_period",
            "timeseries": 1,
            "time_interval": "Monthly",
            "timespan": "Last Year",
            "type": "Line",
            "color": "#F18F01",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        # SDG Progress by Goal
        {
            "doctype": "Dashboard Chart",
            "name": "SDG Progress by Goal",
            "chart_name": "SDG Progress by Goal",
            "chart_type": "Custom",
            "source": "SDG Progress Analysis",
            "type": "Bar",
            "color": "#C73E1D",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        # Health Metrics Trend
        {
            "doctype": "Dashboard Chart",
            "name": "Health Metrics Trend",
            "chart_name": "Health Metrics Trend",
            "chart_type": "Count",
            "document_type": "Sustainability Entry",
            "based_on": "reporting_period",
            "timeseries": 1,
            "time_interval": "Monthly",
            "timespan": "Last Year",
            "type": "Line",
            "color": "#3A86FF",
            "is_public": 1,
            "module": "Sdg Reporting",
            "filters_json": json.dumps([["Sustainability Metric", "sdg_link", "=", "3"]]),
        },
        # Health Indicators Comparison
        {
            "doctype": "Dashboard Chart",
            "name": "Health Indicators Comparison",
            "chart_name": "Health Indicators Comparison",
            "chart_type": "Custom",
            "source": "Health Indicators Analysis",
            "type": "Bar",
            "color": "#06FFA5",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        # Health Coverage Analysis
        {
            "doctype": "Dashboard Chart",
            "name": "Health Coverage Analysis",
            "chart_name": "Health Coverage Analysis",
            "chart_type": "Sum",
            "document_type": "Sustainability Entry",
            "based_on": "reporting_period",
            "value_based_on": "value",
            "timeseries": 1,
            "time_interval": "Quarterly",
            "timespan": "Last Year",
            "type": "Line",
            "color": "#FFBE0B",
            "is_public": 1,
            "module": "Sdg Reporting",
            "filters_json": json.dumps([["Sustainability Metric", "metric_name", "like", "%coverage%"]]),
        },
        # ESG to SDG Mapping
        {
            "doctype": "Dashboard Chart",
            "name": "ESG to SDG Mapping",
            "chart_name": "ESG to SDG Mapping",
            "chart_type": "Custom",
            "source": "ESG SDG Mapping Analysis",
            "type": "Heatmap",
            "color": "#FB5607",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        # Environmental Metrics
        {
            "doctype": "Dashboard Chart",
            "name": "Environmental Metrics",
            "chart_name": "Environmental Metrics",
            "chart_type": "Count",
            "document_type": "Sustainability Entry",
            "based_on": "reporting_period",
            "timeseries": 1,
            "time_interval": "Monthly",
            "timespan": "Last Year",
            "type": "Line",
            "color": "#8338EC",
            "is_public": 1,
            "module": "Sdg Reporting",
            "filters_json": json.dumps([["Sustainability Metric", "esg_pillar", "like", "%Environmental%"]]),
        },
        # Social Metrics
        {
            "doctype": "Dashboard Chart",
            "name": "Social Metrics",
            "chart_name": "Social Metrics",
            "chart_type": "Count",
            "document_type": "Sustainability Entry",
            "based_on": "reporting_period",
            "timeseries": 1,
            "time_interval": "Monthly",
            "timespan": "Last Year",
            "type": "Line",
            "color": "#3A86FF",
            "is_public": 1,
            "module": "Sdg Reporting",
            "filters_json": json.dumps([["Sustainability Metric", "esg_pillar", "like", "%Social%"]]),
        },
        # Governance Metrics
        {
            "doctype": "Dashboard Chart",
            "name": "Governance Metrics",
            "chart_name": "Governance Metrics",
            "chart_type": "Group By",
            "document_type": "Sustainability Metric",
            "based_on": "sdg_link",
            "value_based_on": "name",
            "group_by_type": "Count",
            "group_by_based_on": "sdg_link",
            "type": "Bar",
            "color": "#77ecca",
            "is_public": 1,
            "module": "Sdg Reporting",
            "filters_json": json.dumps([["Sustainability Metric", "esg_pillar", "like", "%Governance%"]]),
        },
    ]


def get_number_cards():
    """Return number card configurations"""
    return [
        # Total SDG Goals
        {
            "doctype": "Number Card",
            "name": "Total SDG Goals",
            "label": "Total SDG Goals",
            "type": "Document Type",
            "document_type": "SDG Goal",
            "function": "Count",
            "color": "#2E86AB",
            "is_public": 1,
            "module": "Sdg Reporting",
            "show_percentage_stats": 1,
            "stats_time_interval": "Monthly",
        },
        # Active Sustainability Metrics
        {
            "doctype": "Number Card",
            "name": "Active Sustainability Metrics",
            "label": "Active Sustainability Metrics",
            "type": "Document Type",
            "document_type": "Sustainability Metric",
            "function": "Count",
            "color": "#A23B72",
            "is_public": 1,
            "module": "Sdg Reporting",
            "show_percentage_stats": 1,
            "stats_time_interval": "Monthly",
        },
        # Total Sustainability Entries
        {
            "doctype": "Number Card",
            "name": "Total Sustainability Entries",
            "label": "Total Sustainability Entries",
            "type": "Document Type",
            "document_type": "Sustainability Entry",
            "function": "Count",
            "color": "#F18F01",
            "is_public": 1,
            "module": "Sdg Reporting",
            "show_percentage_stats": 1,
            "stats_time_interval": "Monthly",
        },
        # ESG Alignment Percentage
        {
            "doctype": "Number Card",
            "name": "ESG Alignment Percentage",
            "label": "ESG Alignment %",
            "type": "Custom",
            "method": "sdg_reporting.sdg_reporting.api.dashboard.get_esg_alignment_percentage",
            "color": "#C73E1D",
            "is_public": 1,
            "module": "Sdg Reporting",
            "show_percentage_stats": 1,
            "stats_time_interval": "Monthly",
        },
        # Health Metrics Count
        {
            "doctype": "Number Card",
            "name": "Health Metrics Count",
            "label": "Health Metrics",
            "type": "Document Type",
            "document_type": "Sustainability Metric",
            "function": "Count",
            "color": "#3A86FF",
            "is_public": 1,
            "module": "Sdg Reporting",
            "filters_json": json.dumps([["Sustainability Metric", "sdg_link", "=", "3"]]),
            "show_percentage_stats": 1,
            "stats_time_interval": "Monthly",
        },
        # Latest Health Score
        {
            "doctype": "Number Card",
            "name": "Latest Health Score",
            "label": "Latest Health Score",
            "type": "Custom",
            "method": "sdg_reporting.sdg_reporting.api.dashboard.get_latest_health_score",
            "color": "#06FFA5",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        # Health Entries This Month
        {
            "doctype": "Number Card",
            "name": "Health Entries This Month",
            "label": "Health Entries This Month",
            "type": "Document Type",
            "document_type": "Sustainability Entry",
            "function": "Count",
            "color": "#FFBE0B",
            "is_public": 1,
            "module": "Sdg Reporting",
            "filters_json": json.dumps([
                ["Sustainability Metric", "sdg_link", "=", "3"],
                ["Sustainability Entry", "reporting_period", ">=", "2024-01-01"]
            ]),
        },
        # Health Target Achievement
        {
            "doctype": "Number Card",
            "name": "Health Target Achievement",
            "label": "Health Target Achievement %",
            "type": "Custom",
            "method": "sdg_reporting.sdg_reporting.api.dashboard.get_health_target_achievement",
            "color": "#FB5607",
            "is_public": 1,
            "module": "Sdg Reporting",
        },
        # Environmental Metrics Count
        {
            "doctype": "Number Card",
            "name": "Environmental Metrics Count",
            "label": "Environmental Metrics",
            "type": "Document Type",
            "document_type": "Sustainability Metric",
            "function": "Count",
            "color": "#8338EC",
            "is_public": 1,
            "module": "Sdg Reporting",
            "filters_json": json.dumps([["Sustainability Metric", "esg_pillar", "like", "%Environmental%"]]),
        },
        # Social Metrics Count
        {
            "doctype": "Number Card",
            "name": "Social Metrics Count",
            "label": "Social Metrics",
            "type": "Document Type",
            "document_type": "Sustainability Metric",
            "function": "Count",
            "color": "#3A86FF",
            "is_public": 1,
            "module": "Sdg Reporting",
            "filters_json": json.dumps([["Sustainability Metric", "esg_pillar", "like", "%Social%"]]),
        },
        # Governance Metrics Count
        {
            "doctype": "Number Card",
            "name": "Governance Metrics Count",
            "label": "Governance Metrics",
            "type": "Document Type",
            "document_type": "Sustainability Metric",
            "function": "Count",
            "color": "#77ecca",
            "is_public": 1,
            "module": "Sdg Reporting",
            "filters_json": json.dumps([["Sustainability Metric", "esg_pillar", "like", "%Governance%"]]),
        },
        # Total ESG Criteria
        {
            "doctype": "Number Card",
            "name": "Total ESG Criteria",
            "label": "Total ESG Criteria",
            "type": "Document Type",
            "document_type": "ESG Criterion",
            "function": "Count",
            "color": "#2E86AB",
            "is_public": 1,
            "module": "Sdg Reporting",
            "show_percentage_stats": 1,
            "stats_time_interval": "Monthly",
        },
    ]
