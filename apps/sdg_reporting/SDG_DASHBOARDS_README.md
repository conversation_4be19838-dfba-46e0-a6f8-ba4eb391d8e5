# SDG Reporting Dashboards

This document describes the SDG (Sustainable Development Goals) dashboards created for the SDG Reporting module, following Frappe/ERPNext dashboard patterns.

## Overview

The SDG dashboards provide comprehensive visualization and tracking of sustainability metrics aligned with the UN Sustainable Development Goals. The implementation follows Frappe's standard dashboard architecture using Dashboard Charts, Number Cards, and Dashboard configurations.

## Dashboards Created

### 1. SDG Overview Dashboard
**Route:** `/app/dashboard-view/SDG%20Overview`

**Purpose:** Provides a high-level view of all SDG goals and overall progress

**Components:**
- **Number Cards:**
  - Total SDG Goals
  - Active Sustainability Metrics  
  - Total Sustainability Entries
  - ESG Alignment Percentage

- **Charts:**
  - SDG Goal Distribution (Donut chart)
  - ESG Pillar Distribution (Bar chart)
  - Sustainability Metrics Trend (Line chart)
  - SDG Progress by Goal (Custom bar chart)

### 2. SDG Health Metrics Dashboard  
**Route:** `/app/dashboard-view/SDG%20Health%20Metrics`

**Purpose:** Focuses specifically on SDG 3 (Good Health and Well-being) metrics

**Components:**
- **Number Cards:**
  - Health Metrics Count
  - Latest Health Score
  - Health Entries This Month
  - Health Target Achievement

- **Charts:**
  - Health Metrics Trend (Line chart)
  - Health Indicators Comparison (Custom bar chart)
  - Health Coverage Analysis (Line chart)

### 3. ESG Mapping Dashboard
**Route:** `/app/dashboard-view/ESG%20Mapping`

**Purpose:** Shows the relationship between ESG criteria and SDG goals

**Components:**
- **Number Cards:**
  - Environmental Metrics Count
  - Social Metrics Count
  - Governance Metrics Count
  - Total ESG Criteria

- **Charts:**
  - ESG to SDG Mapping (Custom heatmap)
  - Environmental Metrics (Line chart)
  - Social Metrics (Line chart)
  - Governance Metrics (Bar chart)

## Technical Implementation

### Dashboard Architecture

The dashboards follow Frappe's standard pattern:

1. **Dashboard Chart Sources** - Custom data sources for complex charts
2. **Dashboard Charts** - Individual chart configurations
3. **Number Cards** - KPI cards with metrics
4. **Dashboards** - Main dashboard configurations linking charts and cards

### Key Files

```
apps/sdg_reporting/sdg_reporting/sdg_reporting/
├── dashboard_fixtures.py                    # Dashboard configurations
├── api/dashboard.py                         # API methods for number cards
├── setup/install.py                         # Installation script
├── setup_dashboards.py                     # Manual setup script
└── dashboard_chart_source/                 # Custom chart sources
    ├── sdg_progress_analysis/
    ├── health_indicators_analysis/
    └── esg_sdg_mapping_analysis/
```

### Data Sources

The dashboards pull data from:
- **Sustainability Metric** - Core metrics linked to SDG goals and ESG pillars
- **Sustainability Entry** - Actual data entries with values and reporting periods
- **SDG Goal** - SDG goal definitions
- **ESG Criterion** - ESG criteria definitions

## Installation

### Automatic Installation
The dashboards are automatically created when the SDG Reporting app is installed via the `after_install` hook.

### Manual Installation
If you need to manually setup the dashboards:

```bash
# From bench console
bench --site [site-name] console

# Run the setup script
>>> exec(open('apps/sdg_reporting/sdg_reporting/sdg_reporting/setup_dashboards.py').read())
```

### Sample Data
The installation script also creates sample data including:
- Sample SDG Goals (1-5)
- Sample ESG Criteria
- Sample Sustainability Metrics

## Customization

### Adding New Charts
1. Create chart configuration in `dashboard_fixtures.py`
2. For custom charts, create a new dashboard chart source
3. Update the dashboard configuration to include the new chart

### Adding New Number Cards
1. Add card configuration to `get_number_cards()` in `dashboard_fixtures.py`
2. For custom calculations, add methods to `api/dashboard.py`
3. Update dashboard configuration to include the new card

### Modifying Existing Dashboards
1. Update configurations in `dashboard_fixtures.py`
2. Re-run the setup script to apply changes

## Permissions

Dashboards inherit permissions from the underlying doctypes:
- Users need read access to "Sustainability Metric" to view dashboards
- Chart and card visibility depends on document-level permissions
- All dashboard components are marked as `is_public: 1` for general access

## Usage

1. **Navigate to Dashboards:**
   - Go to Desk → Dashboard
   - Select from available SDG dashboards

2. **Filter Data:**
   - Use built-in date filters on time-series charts
   - Charts automatically filter based on configured criteria

3. **Drill Down:**
   - Click on chart elements to drill down to underlying data
   - Number cards show percentage changes over time

## Troubleshooting

### Dashboard Not Showing
- Verify the app is installed correctly
- Check if sample data exists
- Ensure proper permissions on Sustainability Metric doctype

### Charts Not Loading
- Check if dashboard chart sources are created
- Verify data exists in Sustainability Entry doctype
- Check browser console for JavaScript errors

### Number Cards Showing Zero
- Verify data exists in underlying doctypes
- Check if custom API methods are working
- Review filters in number card configurations

## Future Enhancements

Potential improvements:
1. Real-time data updates
2. Export functionality for charts
3. Custom date range filters
4. Drill-down to detailed reports
5. Mobile-responsive optimizations
6. Integration with external SDG data sources

## Support

For issues or questions:
1. Check Frappe documentation for dashboard troubleshooting
2. Review error logs in Error Log doctype
3. Verify data integrity in source doctypes
