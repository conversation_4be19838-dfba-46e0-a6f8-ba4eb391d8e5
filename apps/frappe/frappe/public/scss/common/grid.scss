@mixin grid-focus() {
	outline: none;
	border: 1px solid var(--gray-400) !important;
}
.form-grid {
	border: 1px solid var(--table-border-color);
	border-radius: var(--border-radius-md);
	color: var(--text-color);
	&:focus-visible {
		@include grid-focus();
	}
}

.form-grid.error {
	border-color: var(--error-border);
}

.grid-heading-row {
	border-bottom: 1px solid var(--table-border-color);
	color: var(--gray-600);
	background-color: var(--subtle-fg);
	@include get_textstyle("sm", "regular");
	height: 32px;
	padding: 0 !important;
	border-top-left-radius: var(--border-radius-md);
	border-top-right-radius: var(--border-radius-md);

	&.with-filter {
		height: 64px;
	}
	.filter-row {
		background-color: var(--bg-color);

		.search {
			// TODO: Align with table grid without overwriting padding if possible
			padding: 4px 7px 4px 7px !important;
			border-bottom: 1px solid var(--table-border-color);
		}
	}

	.grid-static-col,
	.row-check,
	.row-index {
		height: 32px;
		padding: 6px 8px !important;
	}
	.grid-static-col {
		padding: 6px 8px !important;
		.static-area {
			&.reqd:after {
				content: " *";
				color: var(--red-400);
			}
		}
	}
}

.rows .grid-row .data-row,
.rows .grid-row .grid-footer-toolbar,
.grid-form-heading {
	cursor: pointer;
}

.data-row textarea {
	height: 38px;
}

.form-grid .data-row {
	// padding: 10px 15px;

	&.highlight {
		background-color: var(--yellow-highlight-color);
	}

	&.sortable-handle {
		cursor: move;
	}
}

// hide row index in 6/4 column child tables
.form-column.col-sm-6,
.form-column.col-sm-4 {
	.form-grid {
		.row-index {
			display: none;
		}
	}
}

.modal .form-grid .row-index {
	display: none;
}

.form-grid .grid-heading-row .template-row {
	margin-left: 8px;
}

.form-grid .template-row {
	width: calc(100% - 30px);
	padding: 8px 15px;
}

.grid-body .data-row {
	@include get_textstyle("sm", "regular");
	color: var(--text-muted);
	button.col:focus-visible {
		outline: 1px solid #c9c9c9;
	}
}

.grid-empty,
.list-loading {
	@include flex(flex, center, center, column);
	padding: var(--padding-lg) var(--padding-sm);
	color: var(--text-muted);

	img {
		margin-bottom: var(--margin-sm);
	}
}

.grid-static-col,
.row-index {
	height: 38px;
	max-height: 200px;

	&.search {
		padding: 7px !important;

		input {
			height: -webkit-fill-available;
			padding: 3px 7px;
		}
	}
}

.row-check {
	height: 38px;
	text-align: center;

	input {
		margin-right: 0 !important;
	}

	&.search {
		padding: var(--grid-padding) !important;
	}
}

.grid-row-check {
	margin-top: 2px;

	&::after {
		// Extend the checkbox's clickable area
		display: block;
		content: "";
		inset: -8px;
		position: absolute;
		opacity: 0;
	}
}

.template-row-index {
	float: left;
	margin-left: 15px;
	margin-top: 8px;
	margin-right: -20px;

	span {
		margin-left: 5px;
	}
}

.editable-form .grid-static-col.bold {
	font-weight: bold;
}

.editable-form .grid-static-col.invalid {
	background-color: var(--error-bg);
}

.validated-form .grid-static-col.error {
	background-color: var(--error-bg);
}

.row-index {
	text-align: center;
}

.grid-row > .row {
	.col:last-child {
		border-right: none;
		min-width: 0;
	}

	.col {
		padding: var(--grid-padding);
		border-right: 1px solid var(--table-border-color);
		// overflow: hidden;
	}
}

.grid-body {
	background-color: var(--fg-color);
	border-bottom-left-radius: var(--border-radius);
	border-bottom-right-radius: var(--border-radius);

	.col:last-child {
		border: none;
	}

	.btn-open-row {
		display: flex;
		justify-content: center;
		line-height: unset;
		div {
			margin-left: var(--margin-xs);
		}
	}

	.editable-row {
		// TODO: Dark and Light Mode
		--control-bg: var(--neutral);
		--input-disabled-bg: var(--gray-50);
		.grid-static-col {
			padding: 0px !important;
		}

		.frappe-control[data-fieldtype="Select"].form-group .select-icon {
			top: 9px;
		}

		.checkbox {
			margin: 0px;
			text-align: center;
			margin-top: var(--padding-sm);
		}

		textarea {
			height: 46px !important;
		}

		.form-control,
		.ql-editor {
			border-radius: 0px;
			border: 0px;
			padding-top: 10px;
			padding-bottom: 10px;
			height: 38px;
		}

		.link-btn {
			top: 5px;
			background-color: var(--bg-color);
		}

		.form-control:focus {
			border-color: $text-muted;
			z-index: 2;
		}

		.has-error .form-control {
			z-index: 1;
			&:focus {
				border-color: var(--error-border);
			}
		}

		input.form-control.invalid {
			background-color: var(--error-bg);
		}

		input[data-fieldtype="Int"],
		input[data-fieldtype="Float"],
		input[data-fieldtype="Currency"] {
			text-align: right;
		}
	}

	.grid-static-col[data-fieldtype="Button"] .field-area {
		margin-top: var(--margin-xs);
		margin-left: var(--margin-xs);

		button {
			height: 24px;
		}
	}

	.grid-static-col[data-fieldtype="Check"] .static-area {
		padding-top: 2px;
	}

	.grid-static-col[data-fieldtype="Rating"] .field-area {
		margin-top: 1rem;
		margin-left: 1rem;
	}

	.grid-static-col[data-fieldtype="Code"],
	.grid-static-col[data-fieldtype="HTML Editor"] {
		overflow: hidden;

		.static-area {
			margin-top: calc(-1 * var(--margin-xs));

			pre {
				background: none;
				border: none;
			}
		}
	}

	.grid-static-col[data-fieldtype="Text Editor"] {
		overflow: hidden;
		margin: 0 !important;

		.ql-editor {
			overflow-y: auto !important;
			min-height: 0 !important;
			max-height: unset !important;
			line-height: 1.3 !important;
		}
	}
}

@media (max-width: 767px) {
	// lesser padding for controls
	.editable-row .frappe-control {
		padding-top: 0px !important;
		padding-bottom: 0px !important;
		margin-left: -1px !important;
		margin-right: -1px !important;
	}
}

.row-data > .row {
	margin-left: var(--margin-md);
}

.grid-row {
	padding: 0px var(--padding-md);
	border-bottom: 1px solid var(--table-border-color);
	@include transition(0.2s);
	// REDESIGN TODO: Fix border bottom for last item
	&:last-child {
		border-bottom-left-radius: var(--border-radius);
		border-bottom-right-radius: var(--border-radius);
		border: none;
	}

	td {
		vertical-align: top;
	}

	p {
		margin-bottom: var(--margin-xs);
	}

	.frappe-control {
		margin-bottom: 0px;
		position: relative;
	}

	.col-sm-6 {
		.editor-toolbar-text-group,
		.editor-toolbar-align-group {
			display: none;
		}
	}

	.editable-row {
		.markdown-container {
			position: relative;
			z-index: 1;
		}
	}
}

@mixin base-grid() {
	background-color: var(--modal-bg);
	position: relative;
	transition: opacity 0.2s ease;
}

.form-in-grid {
	overflow: hidden;
	height: 0;
	opacity: 0;
	z-index: 1021;
	border-radius: var(--border-radius-md);
	@include base-grid();

	.panel-title {
		line-height: 1.8em;
	}

	.btn {
		box-shadow: none;
	}

	.form-section {
		padding: var(--padding-md);

		&:not(:first-child) {
			border-top: 1px solid var(--border-color);
		}

		.section-head {
			padding: 0;
			@include get_textstyle("lg", "medium");
		}

		.form-column:first-child {
			padding-left: 0;
		}
	}

	.grid-delete-row {
		.icon use {
			stroke: var(--fg-color);
		}
	}

	.grid-append-row {
		margin-top: calc(-1 * var(--margin-xs));
	}
}

#freeze.grid-form {
	z-index: 1020;
}

.recorder-form-in-grid {
	z-index: 0;
	@include base-grid();
}

.grid-row-open .form-in-grid {
	opacity: 1;
	height: auto;
	overflow: visible;
	margin: 0px calc(-1 * var(--margin-md));
	padding: var(--padding-sm) var(--padding-md);
}

.grid-form-heading {
	padding: var(--padding-sm) var(--padding-md);
	font-size: var(--text-xl);
	font-weight: 600;
	// border-bottom: 1px solid var(--border-color);
}

.grid-form-body {
	.form-area.scrollable {
		max-height: calc(100vh - 3.5rem);
		overflow-y: auto;
	}
}

.grid-header-toolbar {
	display: flex;
	justify-content: space-between;
	.row-actions {
		button:not(:last-child) {
			margin-left: var(--margin-xs);
			margin-bottom: var(--margin-xs);
		}
	}
}

.grid-buttons,
.grid-bulk-actions {
	display: inline-flex;
}

.grid-custom-buttons:empty {
	padding: 0;
}
.grid-footer {
	margin-top: var(--margin-sm);
}
.grid-footer,
.grid-custom-buttons {
	padding: var(--padding-sm) 0px;
	background-color: var(--fg-color);
	.btn {
		box-shadow: none;
		margin-top: -3px;
		margin-bottom: -3px;
	}

	.btn:not(:last-child) {
		margin-right: 4px;
	}
}

.grid-pagination {
	padding: 0;
}

.page-text {
	display: inline-block;
	cursor: default;
}

.current-page-number {
	width: 16px;
	text-align: center;
	border: none;
	cursor: text;

	&:focus {
		outline: none;
	}
}

.prev-page,
.next-page {
	.icon {
		width: 10px;
	}
}

.prev-page {
	margin-left: var(--margin-xs);
	text-decoration: none;
}

.next-page {
	margin-right: var(--margin-xs);
	text-decoration: none;
}

.page-number {
	background-color: var(--fg-color);
	padding: 0 3px;
}

.grid-footer-toolbar {
	padding: var(--padding-md) var(--padding-sm) var(--padding-xs) var(--padding-sm);
	// border-top: 1px solid var(--border-color);
	span {
		margin-right: var(--margin-xs);
	}

	button {
		margin-left: var(--margin-xs);
	}
}

.grid-overflow-no-ellipsis {
	word-wrap: break-word;
	overflow: hidden;
	padding-right: 0px;
}

.grid-overflow-ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-right: 0px;
}

.grid-label {
	margin-right: 8px;
	margin-bottom: 4px;
}

@media (max-width: map-get($grid-breakpoints, "md")) {
	.form-grid-container {
		overflow-x: clip;

		.form-grid {
			min-width: 738px;
		}
	}

	.form-column.col-sm-6 .form-grid {
		.row-index {
			display: block;
		}
	}
}

@media (min-width: map-get($grid-breakpoints, "md")) {
	.form-grid-container {
		overflow-x: unset !important;

		.form-grid {
			position: unset !important;
		}
	}
}

@media (max-width: map-get($grid-breakpoints, "sm")) {
	.form-in-grid .form-section .form-column {
		padding-left: 0 !important;
		padding-right: 0 !important;
	}
}
