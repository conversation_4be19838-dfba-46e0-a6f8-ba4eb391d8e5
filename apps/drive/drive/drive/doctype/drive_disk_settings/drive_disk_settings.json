{"actions": [], "allow_rename": 1, "creation": "2025-04-09 12:48:31.850910", "doctype": "DocType", "engine": "InnoDB", "field_order": ["general_section", "jwt_key", "preview_size", "disk_section", "flat", "root_prefix_type", "root_prefix_value", "team_prefix", "personal_prefix", "thumbnail_prefix", "s3_section", "enabled", "aws_key", "aws_secret", "bucket", "endpoint_url", "signature_version"], "fields": [{"depends_on": "enabled", "fieldname": "aws_key", "fieldtype": "Data", "label": "AWS Key"}, {"depends_on": "enabled", "fieldname": "aws_secret", "fieldtype": "Password", "label": "AWS Secret"}, {"default": "0", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}, {"depends_on": "enabled", "fieldname": "bucket", "fieldtype": "Data", "label": "S3 Bucket"}, {"depends_on": "enabled", "description": "Example format: https://s3.ap-south-1.amazonaws.com", "fieldname": "endpoint_url", "fieldtype": "Data", "label": "Endpoint URL"}, {"default": "s3v4", "depends_on": "enabled", "description": "Defaults to \"s3v4\". Some providers only support \"s3\".", "fieldname": "signature_version", "fieldtype": "Data", "label": "Signature Version"}, {"fieldname": "s3_section", "fieldtype": "Section Break", "label": "S3"}, {"default": "0", "description": "Legacy option.\n<br>\n<br>\nBy default, Drive stores files the way you see it. If this option is checked, the files are store flatly, with randomized names.\n", "fieldname": "flat", "fieldtype": "Check", "label": "Do not mimic folder/file structure"}, {"fieldname": "general_section", "fieldtype": "Section Break", "label": "General"}, {"default": "team_id", "fieldname": "root_prefix_type", "fieldtype": "Select", "label": "Root Prefix Type", "options": "team_id\nteam_name\nother"}, {"depends_on": "root_prefix_type === 'other'", "fieldname": "root_prefix_value", "fieldtype": "Data", "label": "Root Prefix Value"}, {"default": "team", "fieldname": "team_prefix", "fieldtype": "Data", "label": "Team Prefix"}, {"default": "personal", "fieldname": "personal_prefix", "fieldtype": "Data", "label": "Personal Prefix"}, {"default": ".thumbnails", "fieldname": "thumbnail_prefix", "fieldtype": "Data", "label": "Thumbnail prefix"}, {"fieldname": "jwt_key", "fieldtype": "Data", "label": "JWT Key", "options": "Dangerous - if leaked, all files can be accessed."}, {"default": "100", "description": "Used to configure maximum file sizes of in-browser previews (in megabytes).", "fieldname": "preview_size", "fieldtype": "Int", "in_list_view": 1, "label": "Preview size", "reqd": 1}, {"fieldname": "disk_section", "fieldtype": "Section Break", "label": "Disk"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-09-14 17:33:01.169789", "modified_by": "Administrator", "module": "Drive", "name": "Drive Disk Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}